"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { AnimatePresence, motion } from "framer-motion"
import { useSession } from "next-auth/react"
import { useForm } from "react-hook-form"
import { z } from "zod"
import {
  ChevronLeft,
  ChevronRight,
  Loader2,
  Plus,
  Upload,
  X,
} from "lucide-react"

import type { DictionaryType } from "@/lib/get-dictionary"

import { toast } from "@/hooks/use-toast"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import {
  <PERSON>,
  Select<PERSON>ontent,
  Select<PERSON>tem,
  Select<PERSON>rigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"

interface StudentOnboardingProps {
  dictionary: DictionaryType
}

const MAX_FILE_SIZE = 5 * 1024 * 1024 // 5MB
const ACCEPTED_FILE_TYPES = [
  "application/pdf",
  "application/msword",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
]

const studentOnboardingSchema = z.discriminatedUnion("hasBeenMatched", [
  z.object({
    hasBeenMatched: z.literal(true),
    matchedReference: z.string().min(1, "Reference name is required"),
    matchedType: z.enum(["faculty", "organization"]),
  }),
  z.object({
    hasBeenMatched: z.literal(false),
    resume: z
      .instanceof(File)
      .refine(
        (file) => file.size <= MAX_FILE_SIZE,
        "File size must be less than 5MB"
      )
      .refine(
        (file) => ACCEPTED_FILE_TYPES.includes(file.type),
        "Only PDF and Word documents are accepted"
      ),
    coverLetter: z
      .instanceof(File)
      .refine(
        (file) => file.size <= MAX_FILE_SIZE,
        "File size must be less than 5MB"
      )
      .refine(
        (file) => ACCEPTED_FILE_TYPES.includes(file.type),
        "Only PDF and Word documents are accepted"
      )
      .optional(),
    academicYear: z.enum(["first", "second", "third", "fourth", "graduate"]),
    major: z.string().min(1, "Major is required"),
    gpa: z.number().min(0).max(4).optional(),
    skills: z.array(z.string().min(1)).optional(),
    interests: z.string().max(500).optional(),
    availability: z.enum(["fulltime", "parttime", "flexible"]),
  }),
])

type StudentOnboardingData = z.infer<typeof studentOnboardingSchema>

export function StudentOnboarding({
  dictionary: _dictionary,
}: StudentOnboardingProps) {
  const { data: session, update } = useSession()
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)
  const [hasBeenMatched, setHasBeenMatched] = useState<boolean | null>(null)
  const [skills, setSkills] = useState<string[]>([])
  const [newSkill, setNewSkill] = useState("")
  const [resumeFile, setResumeFile] = useState<File | null>(null)
  const [coverLetterFile, setCoverLetterFile] = useState<File | null>(null)

  const form = useForm<StudentOnboardingData>({
    resolver: zodResolver(studentOnboardingSchema),
    defaultValues: {
      hasBeenMatched: false,
      availability: "flexible",
    },
  })

  const handleInitialChoice = (value: boolean) => {
    setHasBeenMatched(value)
    form.setValue("hasBeenMatched", value)
    setCurrentStep(1)
  }

  const addSkill = () => {
    if (newSkill.trim()) {
      const updated = [...skills, newSkill.trim()]
      setSkills(updated)
      form.setValue("skills", updated)
      setNewSkill("")
    }
  }

  const removeSkill = (index: number) => {
    const updated = skills.filter((_, i) => i !== index)
    setSkills(updated)
    form.setValue("skills", updated)
  }

  const handleFileUpload = (
    event: React.ChangeEvent<HTMLInputElement>,
    type: "resume" | "coverLetter"
  ) => {
    const file = event.target.files?.[0]
    if (file) {
      if (type === "resume") {
        setResumeFile(file)
        form.setValue("resume", file)
      } else {
        setCoverLetterFile(file)
        form.setValue("coverLetter", file)
      }
    }
  }

  const onSubmit = async (data: StudentOnboardingData) => {
    setIsSubmitting(true)

    try {
      const formData = new FormData()
      formData.append("userId", session?.user?.id || "")
      formData.append("hasBeenMatched", data.hasBeenMatched.toString())

      if (data.hasBeenMatched) {
        formData.append("matchedReference", data.matchedReference)
        formData.append("matchedType", data.matchedType)
      } else {
        formData.append("resume", data.resume)
        if (data.coverLetter) {
          formData.append("coverLetter", data.coverLetter)
        }
        formData.append("academicYear", data.academicYear)
        formData.append("major", data.major)
        if (data.gpa) {
          formData.append("gpa", data.gpa.toString())
        }
        if (data.skills) {
          formData.append("skills", JSON.stringify(data.skills))
        }
        if (data.interests) {
          formData.append("interests", data.interests)
        }
        formData.append("availability", data.availability)
      }

      const response = await fetch("/api/onboarding/student", {
        method: "POST",
        body: formData,
      })

      if (!response.ok) {
        throw new Error("Failed to complete onboarding")
      }

      // Update session to reflect onboarding completion
      await update()

      toast({
        title: "Welcome to Shadowland!",
        description: "Your student profile has been set up successfully.",
      })

      router.push("/")
    } catch (_error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to complete onboarding. Please try again.",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (currentStep === 0) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="text-2xl">Student Onboarding</CardTitle>
          <CardDescription>Welcome to Shadowland</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-4">
              Have you already been matched to a faculty member or organization?
            </h3>
            <RadioGroup
              onValueChange={(value) => handleInitialChoice(value === "yes")}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="yes" id="yes" />
                <Label htmlFor="yes">Yes, I have been matched</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="no" id="no" />
                <Label htmlFor="no">No, I need to find a match</Label>
              </div>
            </RadioGroup>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl">
          {hasBeenMatched ? "Confirm Your Match" : "Create Your Profile"}
        </CardTitle>
        <CardDescription>
          {hasBeenMatched
            ? "Please provide your match reference information"
            : "Upload your documents and tell us about yourself"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <AnimatePresence mode="wait">
              {hasBeenMatched ? (
                <motion.div
                  key="match-info"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="space-y-4"
                >
                  <FormField
                    control={form.control}
                    name="matchedType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Match Type</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="faculty">
                              Faculty Member
                            </SelectItem>
                            <SelectItem value="organization">
                              Organization
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="matchedReference"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {form.watch("matchedType") === "faculty"
                            ? "Faculty Member Name"
                            : "Organization Name"}
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={
                              form.watch("matchedType") === "faculty"
                                ? "Dr. Jane Smith"
                                : "Acme Corporation"
                            }
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Enter the full name of your matched faculty member or
                          organization
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </motion.div>
              ) : (
                <motion.div
                  key="profile-form"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="space-y-6"
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="resume">Resume (Required)</Label>
                      <div className="mt-2">
                        <label
                          htmlFor="resume"
                          className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer hover:bg-muted/50"
                        >
                          <div className="flex flex-col items-center justify-center pt-5 pb-6">
                            <Upload className="h-8 w-8 mb-2 text-muted-foreground" />
                            <p className="mb-2 text-sm text-muted-foreground">
                              {resumeFile
                                ? resumeFile.name
                                : "Click to upload resume"}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              PDF or Word (MAX. 5MB)
                            </p>
                          </div>
                          <input
                            id="resume"
                            type="file"
                            className="hidden"
                            accept=".pdf,.doc,.docx"
                            onChange={(e) => handleFileUpload(e, "resume")}
                          />
                        </label>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="coverLetter">
                        Cover Letter (Optional)
                      </Label>
                      <div className="mt-2">
                        <label
                          htmlFor="coverLetter"
                          className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer hover:bg-muted/50"
                        >
                          <div className="flex flex-col items-center justify-center pt-5 pb-6">
                            <Upload className="h-8 w-8 mb-2 text-muted-foreground" />
                            <p className="mb-2 text-sm text-muted-foreground">
                              {coverLetterFile
                                ? coverLetterFile.name
                                : "Click to upload cover letter"}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              PDF or Word (MAX. 5MB)
                            </p>
                          </div>
                          <input
                            id="coverLetter"
                            type="file"
                            className="hidden"
                            accept=".pdf,.doc,.docx"
                            onChange={(e) => handleFileUpload(e, "coverLetter")}
                          />
                        </label>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="academicYear"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Academic Year</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select year" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="first">First Year</SelectItem>
                              <SelectItem value="second">
                                Second Year
                              </SelectItem>
                              <SelectItem value="third">Third Year</SelectItem>
                              <SelectItem value="fourth">
                                Fourth Year
                              </SelectItem>
                              <SelectItem value="graduate">Graduate</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="major"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Major</FormLabel>
                          <FormControl>
                            <Input placeholder="Computer Science" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="gpa"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>GPA (Optional)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              min={0}
                              max={4}
                              placeholder="3.50"
                              {...field}
                              onChange={(e) =>
                                field.onChange(parseFloat(e.target.value))
                              }
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div>
                    <Label>Skills (Optional)</Label>
                    <div className="space-y-2 mt-2">
                      {skills.map((skill, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <span className="flex-1 px-3 py-2 bg-muted rounded-md text-sm">
                            {skill}
                          </span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => removeSkill(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      <div className="flex gap-2">
                        <Input
                          value={newSkill}
                          onChange={(e) => setNewSkill(e.target.value)}
                          placeholder="e.g., Python, Research, Data Analysis"
                          onKeyPress={(e: React.KeyboardEvent) =>
                            e.key === "Enter" &&
                            (e.preventDefault(), addSkill())
                          }
                        />
                        <Button
                          type="button"
                          variant="outline"
                          onClick={addSkill}
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add
                        </Button>
                      </div>
                    </div>
                  </div>

                  <FormField
                    control={form.control}
                    name="interests"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Research Interests / Career Goals (Optional)
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Tell us about your interests and goals..."
                            className="resize-none"
                            rows={3}
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Maximum 500 characters
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="availability"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Availability</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select availability" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="fulltime">Full-time</SelectItem>
                            <SelectItem value="parttime">Part-time</SelectItem>
                            <SelectItem value="flexible">Flexible</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </motion.div>
              )}
            </AnimatePresence>

            <div className="flex justify-between pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => setCurrentStep(0)}
              >
                <ChevronLeft className="mr-2 h-4 w-4" />
                Back
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Complete Onboarding
                <ChevronRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
