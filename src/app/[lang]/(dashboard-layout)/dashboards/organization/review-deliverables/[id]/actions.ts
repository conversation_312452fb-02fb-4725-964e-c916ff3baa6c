"use server"

import { revalidatePath } from "next/cache"
import { eq } from "drizzle-orm"

import { db } from "@/drizzle/db"
import { users } from "@/drizzle/schema"
import { DeliverablesService } from "@/lib/services/deliverables-service"

export async function reviewDeliverable(
  deliverableId: string,
  reviewerId: string,
  status: "approved" | "needs_revision",
  feedback: string
) {
  try {
    const result = await DeliverablesService.reviewDeliverable(
      deliverableId,
      reviewerId,
      status,
      feedback
    )

    revalidatePath("/dashboards/faculty/review-deliverables")
    revalidatePath(`/dashboards/faculty/review-deliverables/${deliverableId}`)
    revalidatePath(`/dashboards/student/deliverables/${deliverableId}`)

    return { success: true, ...result }
  } catch (error) {
    console.error("Failed to review deliverable:", error)
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to review deliverable",
    }
  }
}

export async function delegateReview(
  deliverableId: string,
  delegatorId: string,
  delegateEmail: string,
  reason: string
) {
  try {
    // First, find the user by email
    const delegateUser = await db.query.users.findFirst({
      where: eq(users.email, delegateEmail),
    })

    if (!delegateUser) {
      return { success: false, error: "User not found with that email" }
    }

    const delegation = await DeliverablesService.delegateReview(
      deliverableId,
      delegatorId,
      delegateUser.id,
      reason
    )

    revalidatePath("/dashboards/faculty/review-deliverables")
    revalidatePath(`/dashboards/faculty/review-deliverables/${deliverableId}`)

    return { success: true, delegation }
  } catch (error) {
    console.error("Failed to delegate review:", error)
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to delegate review",
    }
  }
}
