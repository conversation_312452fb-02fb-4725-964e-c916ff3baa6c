"use client"

import { use<PERSON><PERSON>back, useEffect, useState } from "react"
import { toast } from "sonner"
import { Briefcase, GraduationCap, Star, User } from "lucide-react"

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { Skeleton } from "@/components/ui/skeleton"
import { StudentProfileDialog } from "./student-profile-dialog"

interface Student {
  id: string
  name: string
  email: string
  studentId: string
  program?: string
  coreGpa?: number | null
  lrwGpa?: number | null
  cumulativeGpa?: number | null
  statementScore?: number
  skills?: string[]
  interests?: string
  availability?: string
}

interface StudentBrowserProps {
  filters?: {
    program?: string
    minScore?: number
    minCoreGpa?: number
    minLrwGpa?: number
    skills?: string[]
    availability?: string
  }
}

export function StudentBrowser({ filters }: StudentBrowserProps) {
  const [students, setStudents] = useState<Student[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const studentsPerPage = 9

  const fetchStudents = useCallback(async () => {
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: studentsPerPage.toString(),
      })

      // Add filters to params
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            value.forEach((v) => params.append(key, v))
          } else {
            params.append(key, value.toString())
          }
        }
      })

      const response = await fetch(
        `/api/organization/available-students?${params}`
      )
      if (response.ok) {
        const data = await response.json()
        setStudents(data.students)
        setTotalPages(data.totalPages)
      }
    } catch (error) {
      console.error("Failed to fetch students:", error)
      toast.error("Failed to load students")
    } finally {
      setIsLoading(false)
    }
  }, [currentPage, filters, studentsPerPage])

  useEffect(() => {
    fetchStudents()
  }, [fetchStudents])

  const handleRequestMatch = async (studentId: string) => {
    try {
      const response = await fetch("/api/organization/match-requests", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ studentId }),
      })

      if (!response.ok) {
        throw new Error("Failed to request match")
      }

      toast.success("Match request sent successfully")
      // Refresh the list
      fetchStudents()
    } catch (error) {
      console.error("Failed to request match:", error)
      toast.error("Failed to send match request")
    }
  }

  if (isLoading) {
    return <StudentBrowserSkeleton />
  }

  const getGpaBadge = (gpa: number | null | undefined, label: string) => {
    if (gpa === null || gpa === undefined) return null
    const color: "default" | "outline" | "secondary" =
      gpa >= 4.0 ? "default" : gpa >= 3.0 ? "outline" : "secondary" // shadcn/ui Badge docs: success→default, warning→outline
    return (
      <Badge variant={color} className="text-xs">
        {label}: {gpa.toFixed(2)}
      </Badge>
    )
  }

  return (
    <>
      <div className="space-y-6">
        {students.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <User className="h-12 w-12 text-muted-foreground mb-4" />
              <p className="text-muted-foreground text-center">
                No students found matching your criteria
              </p>
            </CardContent>
          </Card>
        ) : (
          <>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {students.map((student) => (
                <Card key={student.id} className="overflow-hidden">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarImage
                            src={`/api/avatars?seed=${student.email}`}
                          />
                          <AvatarFallback>
                            {student.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")
                              .toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <CardTitle className="text-lg">
                            {student.name}
                          </CardTitle>
                          <CardDescription className="text-xs">
                            {student.studentId}
                          </CardDescription>
                        </div>
                      </div>
                      {student.statementScore && (
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-yellow-500" />
                          <span className="text-sm font-semibold">
                            {student.statementScore}/25
                          </span>
                        </div>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {student.program && (
                      <div className="flex items-center gap-2 text-sm">
                        <GraduationCap className="h-4 w-4 text-muted-foreground" />
                        <span>{student.program}</span>
                      </div>
                    )}

                    <div className="flex flex-wrap gap-2">
                      {getGpaBadge(student.coreGpa, "Core GPA")}
                      {getGpaBadge(student.lrwGpa, "LRW GPA")}
                      {getGpaBadge(student.cumulativeGpa, "Cumulative GPA")}
                    </div>

                    {student.skills && student.skills.length > 0 && (
                      <div className="space-y-2">
                        <p className="text-xs font-medium text-muted-foreground">
                          Skills
                        </p>
                        <div className="flex flex-wrap gap-1">
                          {student.skills.slice(0, 3).map((skill) => (
                            <Badge
                              key={skill}
                              variant="outline"
                              className="text-xs"
                            >
                              {skill}
                            </Badge>
                          ))}
                          {student.skills.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{student.skills.length - 3}
                            </Badge>
                          )}
                        </div>
                      </div>
                    )}

                    {student.availability && (
                      <div className="flex items-center gap-2 text-sm">
                        <Briefcase className="h-4 w-4 text-muted-foreground" />
                        <span className="capitalize">
                          {student.availability}
                        </span>
                      </div>
                    )}
                  </CardContent>
                  <CardFooter className="gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => setSelectedStudent(student)}
                    >
                      View Profile
                    </Button>
                    <Button
                      size="sm"
                      className="flex-1"
                      onClick={() => handleRequestMatch(student.id)}
                    >
                      Request Match
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>

            {totalPages > 1 && (
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      href="#"
                      onClick={(e: React.MouseEvent) => {
                        e.preventDefault()
                        if (currentPage > 1) setCurrentPage(currentPage - 1)
                      }}
                    />
                  </PaginationItem>
                  {[...Array(totalPages)].map((_, i) => (
                    <PaginationItem key={i}>
                      <PaginationLink
                        href="#"
                        onClick={(e: React.MouseEvent) => {
                          e.preventDefault()
                          setCurrentPage(i + 1)
                        }}
                        isActive={currentPage === i + 1}
                      >
                        {i + 1}
                      </PaginationLink>
                    </PaginationItem>
                  ))}
                  <PaginationItem>
                    <PaginationNext
                      href="#"
                      onClick={(e: React.MouseEvent) => {
                        e.preventDefault()
                        if (currentPage < totalPages)
                          setCurrentPage(currentPage + 1)
                      }}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            )}
          </>
        )}
      </div>

      <StudentProfileDialog
        student={selectedStudent}
        open={!!selectedStudent}
        onOpenChange={(open) => !open && setSelectedStudent(null)}
        onRequestMatch={handleRequestMatch}
      />
    </>
  )
}

function StudentBrowserSkeleton() {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {[...Array(6)].map((_, i) => (
        <Card key={i}>
          <CardHeader>
            <div className="flex items-start gap-3">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="h-5 w-32" />
                <Skeleton className="h-3 w-24" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-3">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <div className="flex gap-2">
              <Skeleton className="h-6 w-20" />
              <Skeleton className="h-6 w-20" />
            </div>
          </CardContent>
          <CardFooter className="gap-2">
            <Skeleton className="h-9 flex-1" />
            <Skeleton className="h-9 flex-1" />
          </CardFooter>
        </Card>
      ))}
    </div>
  )
}
