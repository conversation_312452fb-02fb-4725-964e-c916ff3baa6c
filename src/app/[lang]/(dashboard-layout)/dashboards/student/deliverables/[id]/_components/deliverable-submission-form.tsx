"use client"

import { use<PERSON><PERSON>back, useEffect, useState, useTransition } from "react"
import { useRouter } from "next/navigation"

// Simple debounce function to replace lodash dependency
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

import type {
  deliverableDrafts,
  deliverableRevisions,
  deliverableTemplates,
  deliverables,
  users,
} from "@/drizzle/schema"
import type { InferSelectModel } from "drizzle-orm"

import { toast } from "@/hooks/use-toast"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardH<PERSON>er,
  Card<PERSON><PERSON><PERSON>,
} from "@/components/ui/card"
import { Editor } from "@/components/ui/editor"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { UniversalFileUpload } from "@/components/file-upload/universal-file-upload"
import { Icons } from "@/components/icons"
import { saveDraft, submitDeliverable } from "../actions"

type Deliverable = InferSelectModel<typeof deliverables> & {
  template: InferSelectModel<typeof deliverableTemplates> | null
  revisions: InferSelectModel<typeof deliverableRevisions>[]
  faculty: Pick<
    InferSelectModel<typeof users>,
    "id" | "firstName" | "lastName" | "email"
  > | null
  organization: Pick<
    InferSelectModel<typeof users>,
    "id" | "firstName" | "lastName" | "email"
  > | null
}

type Draft = InferSelectModel<typeof deliverableDrafts> | undefined

interface DeliverableSubmissionFormProps {
  deliverable: Deliverable
  draft: Draft
  userId: string
}

export function DeliverableSubmissionForm({
  deliverable,
  draft,
  userId,
}: DeliverableSubmissionFormProps) {
  const router = useRouter()
  const [content, setContent] = useState(draft?.content || "")
  const [attachments, setAttachments] = useState<
    Array<{
      id: string
      name: string
      url: string
      size: number
      mimeType: string
    }>
  >(draft?.attachments || [])
  const [isSubmitting, startSubmitTransition] = useTransition()
  const [isSavingDraft, startDraftTransition] = useTransition()
  const [lastSaved, setLastSaved] = useState<Date | null>(
    draft ? new Date(draft.lastSavedAt) : null
  )

  // Get the latest revision for viewing feedback
  const latestRevision = deliverable.revisions[0]
  const canSubmit = deliverable.status !== "approved"
  const isOverdue = new Date(deliverable.dueDate) < new Date()

  // Auto-save draft functionality
  const debouncedSaveDraft = useCallback(
    debounce(
      (newContent: string, newAttachments: typeof attachments) => {
        startDraftTransition(async () => {
          try {
            const result = await saveDraft(
              deliverable.id,
              userId,
              newContent,
              newAttachments
            )
            if (result.success) {
              setLastSaved(new Date())
            }
          } catch (error) {
            console.error("Failed to save draft:", error)
          }
        })
      },
      2000 // Save after 2 seconds of inactivity
    ),
    [deliverable.id, userId]
  )

  // Auto-save on content or attachment changes
  useEffect(() => {
    if (content || attachments.length > 0) {
      debouncedSaveDraft(content, attachments)
    }
  }, [content, attachments, debouncedSaveDraft])

  const handleSubmit = async () => {
    if (!content.trim()) {
      toast({
        title: "Error",
        description: "Please provide content for your submission",
        variant: "destructive",
      })
      return
    }

    startSubmitTransition(async () => {
      try {
        const result = await submitDeliverable(
          deliverable.id,
          userId,
          content,
          attachments
        )

        if (result.success) {
          toast({
            title: "Success",
            description: "Your deliverable has been submitted successfully",
          })
          router.push("/dashboards/student/deliverables")
        } else {
          throw new Error(result.error || "Failed to submit")
        }
      } catch (error) {
        toast({
          title: "Error",
          description:
            error instanceof Error
              ? error.message
              : "Failed to submit deliverable",
          variant: "destructive",
        })
      }
    })
  }

  const handleFileUpload = (
    uploadedFiles: Array<{
      id: string
      name: string
      url: string
      size: number
      type: string
      uploadedAt: string
    }>
  ) => {
    const maxAttachments = deliverable.template?.maxAttachments || 1

    if (attachments.length + uploadedFiles.length > maxAttachments) {
      toast({
        title: "Error",
        description: `Maximum ${maxAttachments} attachment${maxAttachments !== 1 ? "s" : ""} allowed`,
        variant: "destructive",
      })
      return
    }

    // Convert uploaded files to our attachment format
    const newAttachments = uploadedFiles.map((file) => ({
      id: file.id,
      name: file.name,
      url: file.url,
      size: file.size,
      mimeType: file.type,
    }))

    setAttachments([...attachments, ...newAttachments])
  }

  const removeAttachment = (index: number) => {
    setAttachments(attachments.filter((_, i) => i !== index))
  }

  return (
    <div className="space-y-6">
      {/* Status and Instructions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Submission Details</CardTitle>
              <CardDescription>
                {deliverable.template?.description || deliverable.description}
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {deliverable.status === "approved" && (
                <Badge variant="success">Approved</Badge>
              )}
              {deliverable.status === "needs_revision" && (
                <Badge variant="warning">Needs Revision</Badge>
              )}
              {deliverable.status === "submitted" && (
                <Badge variant="default">Submitted</Badge>
              )}
              {deliverable.status === "under_review" && (
                <Badge variant="secondary">Under Review</Badge>
              )}
              {deliverable.status === "pending" && !isOverdue && (
                <Badge variant="outline">Pending</Badge>
              )}
              {isOverdue && deliverable.status === "pending" && (
                <Badge variant="destructive">Overdue</Badge>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium mb-2">Instructions</h4>
              <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                {deliverable.template?.instructions ||
                  "Please complete this deliverable according to the requirements."}
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Supervisor:</span>{" "}
                {deliverable.faculty
                  ? `${deliverable.faculty.firstName} ${deliverable.faculty.lastName}`
                  : deliverable.organization
                    ? `${deliverable.organization.firstName} ${deliverable.organization.lastName}`
                    : "Not assigned"}
              </div>
              <div>
                <span className="font-medium">Due Date:</span>{" "}
                {new Date(deliverable.dueDate).toLocaleDateString()}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Revision History and Feedback */}
      {deliverable.revisions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Revision History</CardTitle>
            <CardDescription>
              View previous submissions and feedback
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="latest" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="latest">Latest Feedback</TabsTrigger>
                <TabsTrigger value="history">All Revisions</TabsTrigger>
                <TabsTrigger value="attachments">Previous Files</TabsTrigger>
              </TabsList>

              <TabsContent value="latest" className="space-y-4">
                {latestRevision?.feedback && (
                  <Alert>
                    <Icons.info className="h-4 w-4" />
                    <AlertDescription className="whitespace-pre-wrap">
                      {latestRevision.feedback}
                    </AlertDescription>
                  </Alert>
                )}
                {latestRevision?.status === "approved" && (
                  <Alert className="border-green-200 bg-green-50">
                    <Icons.check className="h-4 w-4 text-green-600" />
                    <AlertDescription className="text-green-800">
                      This deliverable has been approved.
                    </AlertDescription>
                  </Alert>
                )}
              </TabsContent>

              <TabsContent value="history">
                <div className="space-y-2">
                  {deliverable.revisions.map((revision, index) => (
                    <div key={revision.id} className="rounded-lg border p-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">
                          Version {revision.version}
                        </span>
                        <Badge
                          variant={
                            revision.status === "approved"
                              ? "success"
                              : revision.status === "needs_revision"
                                ? "warning"
                                : "default"
                          }
                        >
                          {revision.status}
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        Submitted:{" "}
                        {new Date(revision.submittedAt).toLocaleString()}
                      </p>
                      {revision.feedback && (
                        <p className="text-sm mt-2">{revision.feedback}</p>
                      )}
                    </div>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="attachments">
                <div className="space-y-2">
                  {deliverable.revisions.flatMap(
                    (revision) =>
                      revision.attachments?.map((attachment, index) => (
                        <div
                          key={`${revision.id}-${index}`}
                          className="flex items-center justify-between rounded-lg border p-2"
                        >
                          <div className="flex items-center gap-2">
                            <Icons.file className="h-4 w-4" />
                            <span className="text-sm">{attachment.name}</span>
                            <span className="text-xs text-muted-foreground">
                              (Version {revision.version})
                            </span>
                          </div>
                          <Button variant="ghost" size="sm" asChild>
                            <a
                              href={attachment.url}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              <Icons.download className="h-4 w-4" />
                            </a>
                          </Button>
                        </div>
                      )) || []
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}

      {/* Submission Form */}
      {canSubmit && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Your Submission</CardTitle>
                <CardDescription>
                  Write your response using the rich text editor below
                </CardDescription>
              </div>
              {lastSaved && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Icons.save className="h-4 w-4" />
                  <span>Draft saved {lastSaved.toLocaleTimeString()}</span>
                </div>
              )}
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Rich Text Editor */}
            <div>
              <Editor
                value={content}
                onValueChange={setContent}
                placeholder="Start writing your submission here..."
                className="min-h-[300px]"
              />
            </div>

            {/* File Attachments */}
            {deliverable.template?.requiresAttachment && (
              <div>
                <h4 className="text-sm font-medium mb-2">Attachments</h4>
                <UniversalFileUpload
                  onUpload={handleFileUpload}
                  maxFiles={deliverable.template.maxAttachments || 1}
                  maxSize={10 * 1024 * 1024} // 10MB
                  multiple={deliverable.template.maxAttachments > 1}
                />

                {attachments.length > 0 && (
                  <div className="mt-4 space-y-2">
                    {attachments.map((attachment, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between rounded-lg border p-2"
                      >
                        <div className="flex items-center gap-2">
                          <Icons.file className="h-4 w-4" />
                          <span className="text-sm">{attachment.name}</span>
                          <span className="text-xs text-muted-foreground">
                            ({(attachment.size / 1024 / 1024).toFixed(2)} MB)
                          </span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeAttachment(index)}
                        >
                          <Icons.x className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Submit Button */}
            <div className="flex justify-end gap-4">
              <Button
                variant="outline"
                onClick={() => router.push("/dashboards/student/deliverables")}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={isSubmitting || !content.trim()}
              >
                {isSubmitting && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                {deliverable.revisions.length > 0
                  ? "Submit Revision"
                  : "Submit"}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Approved State */}
      {deliverable.status === "approved" && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Icons.checkCircle className="h-5 w-5 text-green-600" />
              <p className="text-green-800">
                This deliverable has been approved and no further action is
                required.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
