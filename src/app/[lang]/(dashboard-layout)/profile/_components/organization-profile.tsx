"use client"

import { useEffect, useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Building2, MapPin, Upload } from "lucide-react"

import type { DictionaryType } from "@/lib/get-dictionary"
import type { Session } from "next-auth"

import { toast } from "@/hooks/use-toast"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Skeleton } from "@/components/ui/skeleton"
import { Switch } from "@/components/ui/switch"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"

interface OrganizationProfileProps {
  dictionary: DictionaryType
  user: Session["user"]
}

interface ProfileData {
  organizationName: string
  contactFirstName: string
  contactLastName: string
  contactEmail: string
  contactTitle: string
  description?: string
  website?: string
  location?: string
  areasOfLaw?: string[]
  workArrangements?: {
    remote: boolean
    hybrid: boolean
    inPerson: boolean
  }
  logoUrl?: string
}

export function OrganizationProfile({
  dictionary: _dictionary,
  user,
}: OrganizationProfileProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [profileData, setProfileData] = useState<ProfileData>({
    organizationName: "",
    contactFirstName: "",
    contactLastName: "",
    contactEmail: user.email || "",
    contactTitle: "",
    workArrangements: {
      remote: false,
      hybrid: false,
      inPerson: true,
    },
  })
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [logoPreview, setLogoPreview] = useState<string | null>(null)
  const [newArea, setNewArea] = useState("")

  useEffect(() => {
    fetchProfileData()
  }, [])

  const fetchProfileData = async () => {
    try {
      const response = await fetch("/api/organization/profile")
      if (response.ok) {
        const data = await response.json()
        setProfileData(data)
      }
    } catch (_error) {
      console.error("Failed to fetch profile data:", _error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load profile data",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        toast({
          variant: "destructive",
          title: "File too large",
          description: "Logo must be less than 5MB",
        })
        return
      }

      setLogoFile(file)
      const reader = new FileReader()
      reader.onloadend = () => {
        setLogoPreview(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const addAreaOfLaw = () => {
    if (newArea.trim()) {
      setProfileData({
        ...profileData,
        areasOfLaw: [...(profileData.areasOfLaw || []), newArea.trim()],
      })
      setNewArea("")
    }
  }

  const removeAreaOfLaw = (index: number) => {
    setProfileData({
      ...profileData,
      areasOfLaw: profileData.areasOfLaw?.filter((_, i) => i !== index),
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSaving(true)

    try {
      const formData = new FormData()
      Object.entries(profileData).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (typeof value === "object") {
            formData.append(key, JSON.stringify(value))
          } else {
            formData.append(key, value.toString())
          }
        }
      })

      if (logoFile) {
        formData.append("logo", logoFile)
      }

      const response = await fetch("/api/organization/profile", {
        method: "PUT",
        body: formData,
      })

      if (!response.ok) {
        throw new Error("Failed to update profile")
      }

      toast({
        title: "Success",
        description: "Organization profile updated successfully",
      })

      router.refresh()
    } catch (_error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update profile",
      })
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return <ProfileSkeleton />
  }

  const getInitials = () => {
    return profileData.organizationName
      ?.split(" ")
      .map((word) => word[0])
      .slice(0, 2)
      .join("")
      .toUpperCase()
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">
          Organization Profile
        </h1>
        <p className="text-muted-foreground">
          Manage your organization profile and preferences
        </p>
      </div>

      <Tabs defaultValue="organization" className="space-y-4">
        <TabsList>
          <TabsTrigger value="organization">Organization</TabsTrigger>
          <TabsTrigger value="contact">Contact Person</TabsTrigger>
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
        </TabsList>

        <TabsContent value="organization" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Organization Information</CardTitle>
              <CardDescription>
                Update your organization details and logo
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Logo Upload */}
                <div className="flex items-center gap-6">
                  <Avatar className="h-24 w-24 rounded-lg">
                    <AvatarImage
                      src={logoPreview || profileData.logoUrl}
                      alt="Organization Logo"
                    />
                    <AvatarFallback className="rounded-lg">
                      {getInitials() || <Building2 />}
                    </AvatarFallback>
                  </Avatar>
                  <div className="space-y-2">
                    <Label htmlFor="logo" className="cursor-pointer">
                      <Button type="button" variant="outline" size="sm" asChild>
                        <span>
                          <Upload className="h-4 w-4 mr-2" />
                          Change Logo
                        </span>
                      </Button>
                    </Label>
                    <input
                      id="logo"
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={handleLogoChange}
                    />
                    <p className="text-xs text-muted-foreground">
                      JPG, PNG or GIF. Max 5MB.
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="organizationName">Organization Name</Label>
                  <Input
                    id="organizationName"
                    value={profileData.organizationName}
                    onChange={(e) =>
                      setProfileData({
                        ...profileData,
                        organizationName: e.target.value,
                      })
                    }
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={profileData.description || ""}
                    onChange={(e) =>
                      setProfileData({
                        ...profileData,
                        description: e.target.value,
                      })
                    }
                    placeholder="Tell students about your organization..."
                    rows={4}
                  />
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="website">Website</Label>
                    <Input
                      id="website"
                      type="url"
                      value={profileData.website || ""}
                      onChange={(e) =>
                        setProfileData({
                          ...profileData,
                          website: e.target.value,
                        })
                      }
                      placeholder="https://example.com"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="location">Location</Label>
                    <div className="relative">
                      <MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="location"
                        value={profileData.location || ""}
                        onChange={(e) =>
                          setProfileData({
                            ...profileData,
                            location: e.target.value,
                          })
                        }
                        placeholder="City, Province"
                        className="pl-10"
                      />
                    </div>
                  </div>
                </div>

                <Button type="submit" disabled={isSaving}>
                  {isSaving ? "Saving..." : "Save Organization Info"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="contact" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Contact Person</CardTitle>
              <CardDescription>
                Update the primary contact information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="contactFirstName">First Name</Label>
                    <Input
                      id="contactFirstName"
                      value={profileData.contactFirstName}
                      onChange={(e) =>
                        setProfileData({
                          ...profileData,
                          contactFirstName: e.target.value,
                        })
                      }
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="contactLastName">Last Name</Label>
                    <Input
                      id="contactLastName"
                      value={profileData.contactLastName}
                      onChange={(e) =>
                        setProfileData({
                          ...profileData,
                          contactLastName: e.target.value,
                        })
                      }
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="contactTitle">Title/Position</Label>
                  <Input
                    id="contactTitle"
                    value={profileData.contactTitle}
                    onChange={(e) =>
                      setProfileData({
                        ...profileData,
                        contactTitle: e.target.value,
                      })
                    }
                    placeholder="e.g., HR Manager"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="contactEmail">Email</Label>
                  <Input
                    id="contactEmail"
                    type="email"
                    value={profileData.contactEmail}
                    disabled
                    className="bg-muted"
                  />
                  <p className="text-xs text-muted-foreground">
                    Email cannot be changed
                  </p>
                </div>

                <Button type="submit" disabled={isSaving}>
                  {isSaving ? "Saving..." : "Save Contact Info"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preferences" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Preferences</CardTitle>
              <CardDescription>
                Set your areas of law and work arrangements
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Label>Areas of Law</Label>
                  <div className="space-y-2">
                    <div className="flex flex-wrap gap-2">
                      {profileData.areasOfLaw?.map((area, index) => (
                        <Badge
                          key={index}
                          variant="secondary"
                          className="cursor-pointer"
                          onClick={() => removeAreaOfLaw(index)}
                        >
                          {area} ×
                        </Badge>
                      ))}
                    </div>
                    <div className="flex gap-2">
                      <Input
                        value={newArea}
                        onChange={(e) => setNewArea(e.target.value)}
                        placeholder="Add area of law (e.g., Corporate Law)"
                        onKeyPress={(e: React.KeyboardEvent) => {
                          if (e.key === "Enter") {
                            e.preventDefault()
                            addAreaOfLaw()
                          }
                        }}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={addAreaOfLaw}
                      >
                        Add
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <Label>Work Arrangements</Label>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="remote">Remote Work</Label>
                        <p className="text-sm text-muted-foreground">
                          Students can work remotely
                        </p>
                      </div>
                      <Switch
                        id="remote"
                        checked={profileData.workArrangements?.remote}
                        onCheckedChange={(checked) =>
                          setProfileData({
                            ...profileData,
                            workArrangements: {
                              ...profileData.workArrangements!,
                              remote: checked,
                            },
                          })
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="hybrid">Hybrid Work</Label>
                        <p className="text-sm text-muted-foreground">
                          Mix of remote and in-person
                        </p>
                      </div>
                      <Switch
                        id="hybrid"
                        checked={profileData.workArrangements?.hybrid}
                        onCheckedChange={(checked) =>
                          setProfileData({
                            ...profileData,
                            workArrangements: {
                              ...profileData.workArrangements!,
                              hybrid: checked,
                            },
                          })
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="inPerson">In-Person</Label>
                        <p className="text-sm text-muted-foreground">
                          Students work on-site
                        </p>
                      </div>
                      <Switch
                        id="inPerson"
                        checked={profileData.workArrangements?.inPerson}
                        onCheckedChange={(checked) =>
                          setProfileData({
                            ...profileData,
                            workArrangements: {
                              ...profileData.workArrangements!,
                              inPerson: checked,
                            },
                          })
                        }
                      />
                    </div>
                  </div>
                </div>

                <Button type="submit" disabled={isSaving}>
                  {isSaving ? "Saving..." : "Save Preferences"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

function ProfileSkeleton() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="space-y-2">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-4 w-64" />
      </div>
      <Skeleton className="h-96 w-full" />
    </div>
  )
}
