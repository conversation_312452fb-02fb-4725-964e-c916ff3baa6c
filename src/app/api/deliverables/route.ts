import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"
import { db } from "@/drizzle/db"
import { DeliverablesService } from "@/lib/services/deliverables-service"

/**
 * GET /api/deliverables
 * Get deliverables based on user role
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorised" }, { status: 401 })
    }

    let deliverables

    switch (session.user.role) {
      case "student":
        // Students see their own deliverables
        deliverables = await DeliverablesService.getStudentDeliverables(
          session.user.id
        )
        break

      case "faculty":
      case "organization":
        // Faculty and organizations see deliverables they need to review
        deliverables = await DeliverablesService.getDeliverablesForReview(
          session.user.id
        )
        break

      case "admin":
        // <PERSON><PERSON> can see all deliverables
        const { searchParams } = new URL(request.url)
        const studentId = searchParams.get("studentId")

        if (studentId) {
          deliverables =
            await DeliverablesService.getStudentDeliverables(studentId)
        } else {
          // Return all deliverables for admin dashboard
          const result = await db.query.deliverables.findMany({
            with: {
              student: {
                columns: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
              faculty: {
                columns: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
              organization: {
                columns: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
              template: true,
              currentRevision: true,
            },
            orderBy: (deliverables, { desc }) => [desc(deliverables.createdAt)],
          })
          deliverables = result
        }
        break

      default:
        return NextResponse.json(
          { error: "Invalid user role" },
          { status: 403 }
        )
    }

    return NextResponse.json({ deliverables })
  } catch (error) {
    console.error("Failed to fetch deliverables:", error)
    return NextResponse.json(
      { error: "Failed to fetch deliverables" },
      { status: 500 }
    )
  }
}
