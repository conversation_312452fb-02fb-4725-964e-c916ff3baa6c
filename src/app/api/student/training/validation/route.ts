import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions as authConfig } from "@/configs/next-auth"
import { validateTrainingCompletion } from "@/lib/utils/training-validation"

/**
 * GET /api/student/training/validation
 * Validate if current student has completed all required training
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    if (session.user.role !== "student") {
      return NextResponse.json(
        { error: "Only students can access this endpoint" },
        { status: 403 }
      )
    }

    const validationResult = await validateTrainingCompletion(session.user.id)

    return NextResponse.json({
      success: true,
      data: validationResult,
    })
  } catch (error) {
    console.error("Failed to validate training completion:", error)
    return NextResponse.json(
      {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to validate training completion",
      },
      { status: 500 }
    )
  }
}
