import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions as authConfig } from "@/configs/next-auth"
import { TrainingService } from "@/lib/services/training-service"

/**
 * POST /api/student/training/[id]/start
 * Start a training module for the current student
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authConfig)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    if (session.user.role !== "student") {
      return NextResponse.json(
        { error: "Only students can access this endpoint" },
        { status: 403 }
      )
    }

    const progress = await TrainingService.startTraining(
      session.user.id,
      params.id
    )

    return NextResponse.json({
      success: true,
      data: progress,
      message: "Training started successfully",
    })
  } catch (error) {
    console.error("Failed to start training:", error)
    return NextResponse.json(
      {
        success: false,
        error:
          error instanceof Error ? error.message : "Failed to start training",
      },
      { status: 500 }
    )
  }
}
