import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { z } from "zod"

import type { NextRequest } from "next/server"

import { authOptions as authConfig } from "@/configs/next-auth"
import { TrainingService } from "@/lib/services/training-service"

const updateProgressSchema = z.object({
  progress: z.number().min(0).max(100),
  timeSpentMinutes: z.number().min(0).optional().default(0),
})

/**
 * PUT /api/student/training/[id]/progress
 * Update training progress for the current student
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authConfig)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    if (session.user.role !== "student") {
      return NextResponse.json(
        { error: "Only students can access this endpoint" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const validatedData = updateProgressSchema.parse(body)

    const progress = await TrainingService.updateProgress(
      session.user.id,
      params.id,
      validatedData.progress,
      validatedData.timeSpentMinutes
    )

    return NextResponse.json({
      success: true,
      data: progress,
      message: "Progress updated successfully",
    })
  } catch (error) {
    console.error("Failed to update progress:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid data",
          details: error.errors,
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        success: false,
        error:
          error instanceof Error ? error.message : "Failed to update progress",
      },
      { status: 500 }
    )
  }
}
