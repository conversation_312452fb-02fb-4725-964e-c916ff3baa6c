import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { z } from "zod"

import type { NextRequest } from "next/server"

import { authOptions as authConfig } from "@/configs/next-auth"
import { TrainingService } from "@/lib/services/training-service"

const completeTrainingSchema = z.object({
  score: z.number().min(0).max(100).optional(),
})

/**
 * POST /api/student/training/[id]/complete
 * Complete a training module for the current student
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authConfig)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    if (session.user.role !== "student") {
      return NextResponse.json(
        { error: "Only students can access this endpoint" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const validatedData = completeTrainingSchema.parse(body)

    const progress = await TrainingService.completeTraining(
      session.user.id,
      params.id,
      validatedData.score
    )

    const isCompleted = progress.status === "completed"
    const message = isCompleted
      ? "Training completed successfully!"
      : "Training failed. Please try again to meet the minimum score requirement."

    return NextResponse.json({
      success: true,
      data: progress,
      message,
      completed: isCompleted,
    })
  } catch (error) {
    console.error("Failed to complete training:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid data",
          details: error.errors,
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to complete training",
      },
      { status: 500 }
    )
  }
}
