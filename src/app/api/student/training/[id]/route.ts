import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions as authConfig } from "@/configs/next-auth"
import { TrainingService } from "@/lib/services/training-service"

/**
 * GET /api/student/training/[id]
 * Get specific training module with user progress
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authConfig)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    if (session.user.role !== "student") {
      return NextResponse.json(
        { error: "Only students can access this endpoint" },
        { status: 403 }
      )
    }

    const trainingData = await TrainingService.getTrainingModuleWithProgress(
      params.id,
      session.user.id
    )

    return NextResponse.json({
      success: true,
      data: trainingData,
    })
  } catch (error) {
    console.error("Failed to get training module:", error)
    return NextResponse.json(
      {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to get training module",
      },
      { status: 500 }
    )
  }
}
