import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions as authConfig } from "@/configs/next-auth"
import { TrainingService } from "@/lib/services/training-service"

/**
 * GET /api/student/training
 * Get all required training for the current student
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    if (session.user.role !== "student") {
      return NextResponse.json(
        { error: "Only students can access this endpoint" },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const includeStats = searchParams.get("includeStats") === "true"

    // Get required training for user
    const trainingData = await TrainingService.getRequiredTrainingForUser(
      session.user.id
    )

    // Optionally include statistics
    let stats = null
    if (includeStats) {
      stats = await TrainingService.getUserTrainingStats(session.user.id)
    }

    return NextResponse.json({
      success: true,
      data: {
        training: trainingData,
        stats,
      },
    })
  } catch (error) {
    console.error("Failed to get training data:", error)
    return NextResponse.json(
      {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to get training data",
      },
      { status: 500 }
    )
  }
}
