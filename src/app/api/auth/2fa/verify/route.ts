/**
 * API Route: Verify 2FA token during sign-in
 * Used by NextAuth to verify TOTP token or backup code
 */

import { db } from "@/drizzle/db"
import { users } from "@/drizzle/schema"
import { eq } from "drizzle-orm"

import type { NextRequest } from "next/server"

import { Result, err, success } from "@/lib/result"
import {
  decryptBackupCodes,
  decryptTotpSecret,
  encryptBackupCodes,
  verifyTwoFactor,
} from "@/lib/totp-service"

interface VerifyTwoFactorRequest {
  readonly userId: string
  readonly token: string
}

interface VerifyTwoFactorResponse {
  readonly isValid: boolean
  readonly usedBackupCode?: string
}

/**
 * POST /api/auth/2fa/verify
 * Verifies 2FA token during authentication flow
 */
export async function POST(request: NextRequest): Promise<Response> {
  // Parse request body
  let body: VerifyTwoFactorRequest
  const bodyText = await request.text()

  if (!bodyText) {
    return Response.json({ error: "Request body is required" }, { status: 400 })
  }

  const parseResult = (() => {
    const parsed = JSON.parse(bodyText) as VerifyTwoFactorRequest
    if (!parsed.userId || typeof parsed.userId !== "string") {
      return err("User ID is required and must be a string")
    }
    if (!parsed.token || typeof parsed.token !== "string") {
      return err("Token is required and must be a string")
    }
    return success(parsed)
  })()

  if (!parseResult.success) {
    return Response.json({ error: parseResult.error }, { status: 400 })
  }

  body = parseResult.data

  // Get user from database
  const userResult = await db.query.users.findFirst({
    where: eq(users.id, body.userId),
    columns: {
      id: true,
      totpSecret: true,
      totpEnabled: true,
      totpBackupCodes: true,
      totpLastUsed: true,
    },
  })

  if (!userResult) {
    return Response.json({ error: "User not found" }, { status: 404 })
  }

  if (!userResult.totpEnabled || !userResult.totpSecret) {
    return Response.json(
      { error: "2FA is not enabled for this account" },
      { status: 400 }
    )
  }

  // Decrypt the stored secret
  const decryptedSecretResult = decryptTotpSecret(userResult.totpSecret)
  if (!decryptedSecretResult.success) {
    return Response.json(
      { error: `Failed to decrypt secret: ${decryptedSecretResult.error}` },
      { status: 500 }
    )
  }

  // Get backup codes
  let backupCodes: readonly string[] = []
  if (
    userResult.totpBackupCodes &&
    Array.isArray(userResult.totpBackupCodes) &&
    userResult.totpBackupCodes.length > 0
  ) {
    const decryptedCodesResult = decryptBackupCodes(
      userResult.totpBackupCodes[0] as string
    )
    if (decryptedCodesResult.success) {
      backupCodes = decryptedCodesResult.data
    }
  }

  // Verify the provided token
  const lastUsedTimestamp = userResult.totpLastUsed?.getTime()
  const verificationResult = verifyTwoFactor(
    decryptedSecretResult.data,
    body.token,
    backupCodes,
    lastUsedTimestamp
  )

  if (!verificationResult.success) {
    return Response.json(
      { error: `Verification failed: ${verificationResult.error}` },
      { status: 400 }
    )
  }

  if (!verificationResult.data.isValid) {
    return Response.json<VerifyTwoFactorResponse>(
      { isValid: false },
      { status: 200 }
    )
  }

  // Update last used timestamp to prevent token reuse
  const updateResult = await db
    .update(users)
    .set({
      totpLastUsed: new Date(),
      updatedAt: new Date(),
    })
    .where(eq(users.id, body.userId))
    .returning({ id: users.id })
    .then(() => success(true))
    .catch((error) => err(`Database update failed: ${error.message}`))

  if (!updateResult.success) {
    console.error("Failed to update lastUsed timestamp:", updateResult.error)
    // Don't fail the authentication, just log the error
  }

  // If a backup code was used, remove it from the list
  if (verificationResult.data.usedBackupCode) {
    const remainingCodes = backupCodes.filter(
      (code) => code !== verificationResult.data.usedBackupCode
    )

    // Re-encrypt and store remaining codes
    if (remainingCodes.length > 0) {
      const encryptedCodesResult = encryptBackupCodes(remainingCodes)
      if (encryptedCodesResult.success) {
        await db
          .update(users)
          .set({
            totpBackupCodes: [encryptedCodesResult.data],
            updatedAt: new Date(),
          })
          .where(eq(users.id, body.userId))
          .catch((err) => console.error("Failed to update backup codes:", err))
      }
    } else {
      // No backup codes remaining
      await db
        .update(users)
        .set({
          totpBackupCodes: null,
          updatedAt: new Date(),
        })
        .where(eq(users.id, body.userId))
        .catch((err) => console.error("Failed to clear backup codes:", err))
    }
  }

  const response: VerifyTwoFactorResponse = {
    isValid: true,
    ...(verificationResult.data.usedBackupCode && {
      usedBackupCode: verificationResult.data.usedBackupCode,
    }),
  }

  return Response.json(response, { status: 200 })
}
