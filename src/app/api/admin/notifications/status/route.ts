import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"
import { checkNotificationSystemHealth } from "@/lib/init-notification-system"
import { getQueueStatistics, pauseQueue, resumeQueue } from "@/lib/notification-queue"
import { getRedisInfo } from "@/lib/redis-service"
import { isErr } from "@/lib/result"

/**
 * Get notification system status and statistics
 * Admin-only endpoint for monitoring
 */
export async function GET(request: NextRequest) {
  // Verify authentication and admin role
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  // Check if user is admin
  if (session.user.role !== "admin") {
    return NextResponse.json(
      { error: "Forbidden - Admin access required" },
      { status: 403 }
    )
  }

  // Get system health
  const healthResult = await checkNotificationSystemHealth()
  if (isErr(healthResult)) {
    return NextResponse.json(
      { error: `Health check failed: ${healthResult.error.message}` },
      { status: 500 }
    )
  }

  // Get queue statistics
  const statsResult = await getQueueStatistics()
  const queueStats = isErr(statsResult)
    ? { error: statsResult.error.message }
    : statsResult.data

  // Get Redis info
  const redisInfoResult = await getRedisInfo()
  const redisInfo = isErr(redisInfoResult)
    ? { error: redisInfoResult.error.message }
    : redisInfoResult.data

  // Build comprehensive status response
  const response = {
    timestamp: new Date().toISOString(),
    overall_status: healthResult.data.status,
    components: {
      redis: {
        ...healthResult.data.redis,
        ...redisInfo,
      },
      queue: {
        ...healthResult.data.queue,
        statistics: queueStats,
      },
      worker: healthResult.data.worker,
      events: healthResult.data.events,
    },
    summary: {
      healthy_components: Object.values(healthResult.data).filter(Boolean)
        .length,
      total_components: 4,
      uptime: process.uptime(),
      memory_usage: process.memoryUsage(),
    },
  }

  return NextResponse.json(response)
}

/**
 * Pause or resume the notification queue
 * Admin-only endpoint for queue management
 */
export async function POST(request: NextRequest) {
  // Verify authentication and admin role
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  // Check if user is admin
  if (session.user.role !== "admin") {
    return NextResponse.json(
      { error: "Forbidden - Admin access required" },
      { status: 403 }
    )
  }

  // Parse request body safely
  const parseBody = async (): Promise<{ action?: string } | null> => {
    return await Promise.resolve(request.json()).then(
      (data) => data,
      (error) => {
        console.error("Failed to parse request body:", error)
        return null
      }
    )
  }

  const body = await parseBody()
  if (!body) {
    return NextResponse.json({ error: "Invalid request body" }, { status: 400 })
  }

  const { action } = body

  if (!action || !["pause", "resume"].includes(action)) {
    return NextResponse.json(
      { error: 'Invalid action. Must be "pause" or "resume"' },
      { status: 400 }
    )
  }

  // Use imported functions

  if (action === "pause") {
    const pauseResult = await pauseQueue()
    if (isErr(pauseResult)) {
      return NextResponse.json(
        { error: `Failed to pause queue: ${pauseResult.error.message}` },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: "Notification queue paused successfully",
      timestamp: new Date().toISOString(),
    })
  }

  if (action === "resume") {
    const resumeResult = await resumeQueue()
    if (isErr(resumeResult)) {
      return NextResponse.json(
        { error: `Failed to resume queue: ${resumeResult.error.message}` },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: "Notification queue resumed successfully",
      timestamp: new Date().toISOString(),
    })
  }

  // This should never be reached, but TypeScript requires it
  return NextResponse.json({ error: "Unknown error" }, { status: 500 })
}
