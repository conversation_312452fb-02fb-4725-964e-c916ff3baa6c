"use client"

import * as React from "react"
import * as MenubarPrimitive from "@radix-ui/react-menubar"
import { CheckIcon, ChevronRightIcon, CircleIcon } from "lucide-react"

import { cn } from "@/lib/utils"

// Module augmentation for MenubarItemIndicator
declare module "@radix-ui/react-menubar" {
  interface MenubarItemIndicatorProps {
    children?: React.ReactNode
  }
}

const Menubar = React.forwardRef<
  React.ComponentRef<typeof MenubarPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Root> & {
    className?: string
  }
>(({ className, ...props }, ref) => {
  const RootComponent = MenubarPrimitive.Root as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Root> & {
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <RootComponent
      data-slot="menubar"
      ref={ref}
      className={cn(
        "bg-background flex h-9 items-center gap-1 rounded-md border p-1 shadow-xs",
        className
      )}
      {...props}
    />
  )
})
Menubar.displayName = "Menubar"

const MenubarMenu = React.forwardRef<
  React.ComponentRef<typeof MenubarPrimitive.Menu>,
  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Menu>
>(({ ...props }, ref) => {
  const MenuComponent = MenubarPrimitive.Menu as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Menu> & {
      ref?: React.Ref<HTMLDivElement>
    }
  >
  return <MenuComponent data-slot="menubar-menu" ref={ref} {...props} />
})
MenubarMenu.displayName = "MenubarMenu"

const MenubarGroup = React.forwardRef<
  React.ComponentRef<typeof MenubarPrimitive.Group>,
  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Group>
>(({ ...props }, ref) => {
  const GroupComponent = MenubarPrimitive.Group as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Group> & {
      ref?: React.Ref<HTMLDivElement>
    }
  >
  return <GroupComponent data-slot="menubar-group" ref={ref} {...props} />
})
MenubarGroup.displayName = "MenubarGroup"

const MenubarPortal = React.forwardRef<
  React.ComponentRef<typeof MenubarPrimitive.Portal>,
  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Portal>
>(({ ...props }, ref) => {
  const PortalComponent = MenubarPrimitive.Portal as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Portal> & {
      ref?: React.Ref<HTMLDivElement>
    }
  >
  return <PortalComponent data-slot="menubar-portal" ref={ref} {...props} />
})
MenubarPortal.displayName = "MenubarPortal"

const MenubarRadioGroup = React.forwardRef<
  React.ComponentRef<typeof MenubarPrimitive.RadioGroup>,
  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.RadioGroup>
>(({ ...props }, ref) => {
  const RadioGroupComponent = MenubarPrimitive.RadioGroup as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof MenubarPrimitive.RadioGroup> & {
      ref?: React.Ref<HTMLDivElement>
    }
  >
  return (
    <RadioGroupComponent data-slot="menubar-radio-group" ref={ref} {...props} />
  )
})
MenubarRadioGroup.displayName = "MenubarRadioGroup"

const MenubarTrigger = React.forwardRef<
  React.ComponentRef<typeof MenubarPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Trigger> & {
    className?: string
  }
>(({ className, ...props }, ref) => {
  const TriggerComponent = MenubarPrimitive.Trigger as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Trigger> & {
      className?: string
      ref?: React.Ref<HTMLButtonElement>
    }
  >

  return (
    <TriggerComponent
      data-slot="menubar-trigger"
      ref={ref}
      className={cn(
        "focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex items-center rounded-sm px-2 py-1 text-sm font-medium outline-hidden select-none",
        className
      )}
      {...props}
    />
  )
})
MenubarTrigger.displayName = "MenubarTrigger"

const MenubarContent = React.forwardRef<
  React.ComponentRef<typeof MenubarPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Content> & {
    className?: string
    align?: "start" | "center" | "end"
    alignOffset?: number
    sideOffset?: number
  }
>(({ className, align = "start", alignOffset = -4, sideOffset = 8, ...props }, ref) => {
  const ContentComponent = MenubarPrimitive.Content as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Content> & {
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <MenubarPortal>
      <ContentComponent
        data-slot="menubar-content"
        ref={ref}
        align={align}
        alignOffset={alignOffset}
        sideOffset={sideOffset}
        className={cn(
          "bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[12rem] origin-(--radix-menubar-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-md",
          className
        )}
        {...props}
      />
    </MenubarPortal>
  )
})
MenubarContent.displayName = "MenubarContent"

const MenubarItem = React.forwardRef<
  React.ComponentRef<typeof MenubarPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Item> & {
    className?: string
    inset?: boolean
    variant?: "default" | "destructive"
  }
>(({ className, inset, variant = "default", ...props }, ref) => {
  const ItemComponent = MenubarPrimitive.Item as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Item> & {
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <ItemComponent
      data-slot="menubar-item"
      data-inset={inset}
      data-variant={variant}
      ref={ref}
      className={cn(
        "focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
        className
      )}
      {...props}
    />
  )
})
MenubarItem.displayName = "MenubarItem"

const MenubarCheckboxItem = React.forwardRef<
  React.ComponentRef<typeof MenubarPrimitive.CheckboxItem>,
  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.CheckboxItem> & {
    className?: string
    children?: React.ReactNode
  }
>(({ className, children, checked, ...props }, ref) => {
  const CheckboxItemComponent = MenubarPrimitive.CheckboxItem as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof MenubarPrimitive.CheckboxItem> & {
      className?: string
      children?: React.ReactNode
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <CheckboxItemComponent
      data-slot="menubar-checkbox-item"
      ref={ref}
      className={cn(
        "focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-xs py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
        className
      )}
      checked={checked}
      {...props}
    >
      <span className="pointer-events-none absolute left-2 flex size-3.5 items-center justify-center">
        <MenubarPrimitive.ItemIndicator>
          <CheckIcon className="size-4" />
        </MenubarPrimitive.ItemIndicator>
      </span>
      {children}
    </CheckboxItemComponent>
  )
})
MenubarCheckboxItem.displayName = "MenubarCheckboxItem"

const MenubarRadioItem = React.forwardRef<
  React.ComponentRef<typeof MenubarPrimitive.RadioItem>,
  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.RadioItem> & {
    className?: string
    children?: React.ReactNode
  }
>(({ className, children, ...props }, ref) => {
  const RadioItemComponent = MenubarPrimitive.RadioItem as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof MenubarPrimitive.RadioItem> & {
      className?: string
      children?: React.ReactNode
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <RadioItemComponent
      data-slot="menubar-radio-item"
      ref={ref}
      className={cn(
        "focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-xs py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
        className
      )}
      {...props}
    >
      <span className="pointer-events-none absolute left-2 flex size-3.5 items-center justify-center">
        <MenubarPrimitive.ItemIndicator>
          <CircleIcon className="size-2 fill-current" />
        </MenubarPrimitive.ItemIndicator>
      </span>
      {children}
    </RadioItemComponent>
  )
})
MenubarRadioItem.displayName = "MenubarRadioItem"

const MenubarLabel = React.forwardRef<
  React.ComponentRef<typeof MenubarPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Label> & {
    className?: string
    inset?: boolean
  }
>(({ className, inset, ...props }, ref) => {
  const LabelComponent = MenubarPrimitive.Label as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Label> & {
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <LabelComponent
      data-slot="menubar-label"
      data-inset={inset}
      ref={ref}
      className={cn(
        "px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",
        className
      )}
      {...props}
    />
  )
})
MenubarLabel.displayName = "MenubarLabel"

const MenubarSeparator = React.forwardRef<
  React.ComponentRef<typeof MenubarPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Separator> & {
    className?: string
  }
>(({ className, ...props }, ref) => {
  const SeparatorComponent = MenubarPrimitive.Separator as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Separator> & {
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <SeparatorComponent
      data-slot="menubar-separator"
      ref={ref}
      className={cn("bg-border -mx-1 my-1 h-px", className)}
      {...props}
    />
  )
})
MenubarSeparator.displayName = "MenubarSeparator"

const MenubarShortcut = React.forwardRef<
  HTMLSpanElement,
  React.ComponentPropsWithoutRef<"span"> & {
    className?: string
  }
>(({ className, ...props }, ref) => {
  return (
    <span
      data-slot="menubar-shortcut"
      ref={ref}
      className={cn(
        "text-muted-foreground ml-auto text-xs tracking-widest",
        className
      )}
      {...props}
    />
  )
})
MenubarShortcut.displayName = "MenubarShortcut"

const MenubarSub = React.forwardRef<
  React.ComponentRef<typeof MenubarPrimitive.Sub>,
  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Sub>
>(({ ...props }, ref) => {
  const SubComponent = MenubarPrimitive.Sub as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Sub> & {
      ref?: React.Ref<HTMLDivElement>
    }
  >
  return <SubComponent data-slot="menubar-sub" ref={ref} {...props} />
})
MenubarSub.displayName = "MenubarSub"

const MenubarSubTrigger = React.forwardRef<
  React.ComponentRef<typeof MenubarPrimitive.SubTrigger>,
  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.SubTrigger> & {
    className?: string
    inset?: boolean
    children?: React.ReactNode
  }
>(({ className, inset, children, ...props }, ref) => {
  const SubTriggerComponent = MenubarPrimitive.SubTrigger as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof MenubarPrimitive.SubTrigger> & {
      className?: string
      children?: React.ReactNode
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <SubTriggerComponent
      data-slot="menubar-sub-trigger"
      data-inset={inset}
      ref={ref}
      className={cn(
        "focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-none select-none data-[inset]:pl-8",
        className
      )}
      {...props}
    >
      {children}
      <ChevronRightIcon className="ml-auto h-4 w-4" />
    </SubTriggerComponent>
  )
})
MenubarSubTrigger.displayName = "MenubarSubTrigger"

const MenubarSubContent = React.forwardRef<
  React.ComponentRef<typeof MenubarPrimitive.SubContent>,
  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.SubContent> & {
    className?: string
  }
>(({ className, ...props }, ref) => {
  const SubContentComponent = MenubarPrimitive.SubContent as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof MenubarPrimitive.SubContent> & {
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <SubContentComponent
      data-slot="menubar-sub-content"
      ref={ref}
      className={cn(
        "bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-menubar-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg",
        className
      )}
      {...props}
    />
  )
})
MenubarSubContent.displayName = "MenubarSubContent"

export {
  Menubar,
  MenubarPortal,
  MenubarMenu,
  MenubarTrigger,
  MenubarContent,
  MenubarGroup,
  MenubarSeparator,
  MenubarLabel,
  MenubarItem,
  MenubarShortcut,
  MenubarCheckboxItem,
  MenubarRadioGroup,
  MenubarRadioItem,
  MenubarSub,
  MenubarSubTrigger,
  MenubarSubContent,
}
