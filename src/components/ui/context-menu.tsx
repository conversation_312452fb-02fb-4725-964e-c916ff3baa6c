"use client"

import * as React from "react"
import * as ContextMenuPrimitive from "@radix-ui/react-context-menu"
import { CheckIcon, ChevronRightIcon, CircleIcon } from "lucide-react"

import { cn } from "@/lib/utils"

const ContextMenu = React.forwardRef<
  React.ComponentRef<typeof ContextMenuPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Root> & {
    children?: React.ReactNode
    className?: string
  }
>(({ children, className, ...props }, ref) => {
  const RootComponent = ContextMenuPrimitive.Root as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Root> & {
      children?: React.ReactNode
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <RootComponent data-slot="context-menu" ref={ref} className={className} {...props}>
      {children}
    </RootComponent>
  )
})
ContextMenu.displayName = "ContextMenu"

const ContextMenuTrigger = React.forwardRef<
  React.ComponentRef<typeof ContextMenuPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Trigger> & {
    children?: React.ReactNode
    className?: string
  }
>(({ children, className, ...props }, ref) => {
  const TriggerComponent = ContextMenuPrimitive.Trigger as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Trigger> & {
      children?: React.ReactNode
      className?: string
      ref?: React.Ref<HTMLSpanElement>
    }
  >

  return (
    <TriggerComponent data-slot="context-menu-trigger" ref={ref} className={className} {...props}>
      {children}
    </TriggerComponent>
  )
})
ContextMenuTrigger.displayName = ContextMenuPrimitive.Trigger.displayName

const ContextMenuGroup = React.forwardRef<
  React.ComponentRef<typeof ContextMenuPrimitive.Group>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Group> & {
    children?: React.ReactNode
    className?: string
  }
>(({ children, className, ...props }, ref) => {
  const GroupComponent = ContextMenuPrimitive.Group as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Group> & {
      children?: React.ReactNode
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <GroupComponent data-slot="context-menu-group" ref={ref} className={className} {...props}>
      {children}
    </GroupComponent>
  )
})
ContextMenuGroup.displayName = ContextMenuPrimitive.Group.displayName

const ContextMenuPortal = React.forwardRef<
  React.ComponentRef<typeof ContextMenuPrimitive.Portal>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Portal> & {
    children?: React.ReactNode
    className?: string
  }
>(({ children, className, ...props }, ref) => {
  const PortalComponent = ContextMenuPrimitive.Portal as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Portal> & {
      children?: React.ReactNode
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <PortalComponent data-slot="context-menu-portal" ref={ref} className={className} {...props}>
      {children}
    </PortalComponent>
  )
})
ContextMenuPortal.displayName = ContextMenuPrimitive.Portal.displayName

const ContextMenuSub = React.forwardRef<
  React.ComponentRef<typeof ContextMenuPrimitive.Sub>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Sub> & {
    children?: React.ReactNode
    className?: string
  }
>(({ children, className, ...props }, ref) => {
  const SubComponent = ContextMenuPrimitive.Sub as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Sub> & {
      children?: React.ReactNode
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <SubComponent data-slot="context-menu-sub" ref={ref} className={className} {...props}>
      {children}
    </SubComponent>
  )
})
ContextMenuSub.displayName = ContextMenuPrimitive.Sub.displayName

const ContextMenuRadioGroup = React.forwardRef<
  React.ComponentRef<typeof ContextMenuPrimitive.RadioGroup>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.RadioGroup> & {
    children?: React.ReactNode
    className?: string
  }
>(({ children, className, ...props }, ref) => {
  const RadioGroupComponent = ContextMenuPrimitive.RadioGroup as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.RadioGroup> & {
      children?: React.ReactNode
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <RadioGroupComponent data-slot="context-menu-radio-group" ref={ref} className={className} {...props}>
      {children}
    </RadioGroupComponent>
  )
})
ContextMenuRadioGroup.displayName = ContextMenuPrimitive.RadioGroup.displayName

const ContextMenuContent = React.forwardRef<
  React.ComponentRef<typeof ContextMenuPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Content> & {
    children?: React.ReactNode
    className?: string
  }
>(({ children, className, ...props }, ref) => {
  const ContentComponent = ContextMenuPrimitive.Content as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Content> & {
      children?: React.ReactNode
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <ContextMenuPrimitive.Portal>
      <ContentComponent data-slot="context-menu-content" ref={ref} className={className} {...props}>
        {children}
      </ContentComponent>
    </ContextMenuPrimitive.Portal>
  )
})
ContextMenuContent.displayName = ContextMenuPrimitive.Content.displayName

const ContextMenuItem = React.forwardRef<
  React.ComponentRef<typeof ContextMenuPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Item> & {
    children?: React.ReactNode
    className?: string
  }
>(({ children, className, ...props }, ref) => {
  const ItemComponent = ContextMenuPrimitive.Item as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Item> & {
      children?: React.ReactNode
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <ItemComponent data-slot="context-menu-item" ref={ref} className={className} {...props}>
      {children}
    </ItemComponent>
  )
})
ContextMenuItem.displayName = ContextMenuPrimitive.Item.displayName

const ContextMenuCheckboxItem = React.forwardRef<
  React.ComponentRef<typeof ContextMenuPrimitive.CheckboxItem>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.CheckboxItem> & {
    children?: React.ReactNode
    className?: string
  }
>(({ children, className, checked, ...props }, ref) => {
  const CheckboxItemComponent = ContextMenuPrimitive.CheckboxItem as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.CheckboxItem> & {
      children?: React.ReactNode
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <CheckboxItemComponent
      ref={ref}
      data-slot="context-menu-checkbox-item"
      className={cn(
        "focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
        className
      )}
      checked={checked}
      {...props}
    >
      <span className="pointer-events-none absolute left-2 flex size-3.5 items-center justify-center">
        {(ContextMenuPrimitive.ItemIndicator as React.ComponentType<{ children?: React.ReactNode }>)({
          children: <CheckIcon className="size-4" />
        })}
      </span>
      {children}
    </CheckboxItemComponent>
  )
})
ContextMenuCheckboxItem.displayName = ContextMenuPrimitive.CheckboxItem.displayName

const ContextMenuRadioItem = React.forwardRef<
  React.ComponentRef<typeof ContextMenuPrimitive.RadioItem>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.RadioItem> & {
    children?: React.ReactNode
    className?: string
  }
>(({ children, className, ...props }, ref) => {
  const RadioItemComponent = ContextMenuPrimitive.RadioItem as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.RadioItem> & {
      children?: React.ReactNode
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <RadioItemComponent
      ref={ref}
      data-slot="context-menu-radio-item"
      className={cn(
        "focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
        className
      )}
      {...props}
    >
      <span className="pointer-events-none absolute left-2 flex size-3.5 items-center justify-center">
        {(ContextMenuPrimitive.ItemIndicator as React.ComponentType<{ children?: React.ReactNode }>)({
          children: <CircleIcon className="size-2 fill-current" />
        })}
      </span>
      {children}
    </RadioItemComponent>
  )
})
ContextMenuRadioItem.displayName = ContextMenuPrimitive.RadioItem.displayName

const ContextMenuLabel = React.forwardRef<
  React.ComponentRef<typeof ContextMenuPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Label> & {
    children?: React.ReactNode
    className?: string
    inset?: boolean
  }
>(({ children, className, inset, ...props }, ref) => {
  const LabelComponent = ContextMenuPrimitive.Label as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Label> & {
      children?: React.ReactNode
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <LabelComponent data-slot="context-menu-label" data-inset={inset} ref={ref} className={className} {...props}>
      {children}
    </LabelComponent>
  )
})
ContextMenuLabel.displayName = ContextMenuPrimitive.Label.displayName

const ContextMenuSeparator = React.forwardRef<
  React.ComponentRef<typeof ContextMenuPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Separator> & {
    className?: string
  }
>(({ className, ...props }, ref) => {
  const SeparatorComponent = ContextMenuPrimitive.Separator as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Separator> & {
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <SeparatorComponent data-slot="context-menu-separator" ref={ref} className={className} {...props} />
  )
})
ContextMenuSeparator.displayName = ContextMenuPrimitive.Separator.displayName

const ContextMenuShortcut = React.forwardRef<
  HTMLSpanElement,
  React.ComponentPropsWithoutRef<"span"> & {
    children?: React.ReactNode
    className?: string
  }
>(({ children, className, ...props }, ref) => (
  <span
    ref={ref}
    data-slot="context-menu-shortcut"
    className={cn(
      "text-muted-foreground ml-auto text-xs tracking-widest",
      className
    )}
    {...props}
  >
    {children}
  </span>
))
ContextMenuShortcut.displayName = "ContextMenuShortcut"

const ContextMenuSubContent = React.forwardRef<
  React.ComponentRef<typeof ContextMenuPrimitive.SubContent>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.SubContent> & {
    children?: React.ReactNode
    className?: string
  }
>(({ children, className, ...props }, ref) => {
  const SubContentComponent = ContextMenuPrimitive.SubContent as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.SubContent> & {
      children?: React.ReactNode
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <SubContentComponent
      ref={ref}
      data-slot="context-menu-sub-content"
      className={cn(
        "bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-context-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg",
        className
      )}
      {...props}
    >
      {children}
    </SubContentComponent>
  )
})
ContextMenuSubContent.displayName = ContextMenuPrimitive.SubContent.displayName

const ContextMenuSubTrigger = React.forwardRef<
  React.ComponentRef<typeof ContextMenuPrimitive.SubTrigger>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.SubTrigger> & {
    children?: React.ReactNode
    className?: string
    inset?: boolean
  }
>(({ children, className, inset, ...props }, ref) => {
  const SubTriggerComponent = ContextMenuPrimitive.SubTrigger as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.SubTrigger> & {
      children?: React.ReactNode
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <SubTriggerComponent data-slot="context-menu-sub-trigger" data-inset={inset} ref={ref} className={className} {...props}>
      {children}
      <ChevronRightIcon className="ml-auto" />
    </SubTriggerComponent>
  )
})
ContextMenuSubTrigger.displayName = ContextMenuPrimitive.SubTrigger.displayName

export {
  ContextMenu,
  ContextMenuTrigger,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuCheckboxItem,
  ContextMenuRadioItem,
  ContextMenuLabel,
  ContextMenuSeparator,
  ContextMenuShortcut,
  ContextMenuGroup,
  ContextMenuPortal,
  ContextMenuSub,
  ContextMenuSubContent,
  ContextMenuSubTrigger,
  ContextMenuRadioGroup,
}
