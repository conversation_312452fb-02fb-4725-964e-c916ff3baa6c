"use client"

import { useState, useTransition } from "react"
import { formatDistanceToNow } from "date-fns"

import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Editor } from "@/components/ui/editor"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Icons } from "@/components/icons"

/**
 * Conflict Resolution Dialog
 *
 * Implements user-friendly conflict resolution UI based on:
 * - Google Docs conflict resolution patterns
 * - AWS Amplify DataStore conflict UI guidelines
 * - Liveblocks collaborative editing best practices
 */

export interface ConflictData {
  conflictType: "concurrent_edit" | "version_mismatch" | "edit_session_expired"
  clientVersion: number
  serverVersion: number
  conflictingUser?: {
    id: string
    name: string
    email: string
  }
  lastModified?: Date
  conflictDetails?: {
    serverContent: string
    clientContent: string
    diff?: string
  }
}

interface ConflictResolutionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  conflictData: ConflictData | null
  clientContent: string
  onResolve: (
    strategy: "user_merge" | "server_wins" | "client_wins" | "manual",
    content?: string
  ) => Promise<void>
  isResolving?: boolean
}

export function ConflictResolutionDialog({
  open,
  onOpenChange,
  conflictData,
  clientContent,
  onResolve,
  isResolving = false,
}: ConflictResolutionDialogProps) {
  const [selectedStrategy, setSelectedStrategy] = useState<
    "user_merge" | "server_wins" | "client_wins" | "manual"
  >("user_merge")
  const [manualContent, setManualContent] = useState("")
  const [isPending, startTransition] = useTransition()

  if (!conflictData) return null

  const handleResolve = () => {
    startTransition(async () => {
      await onResolve(
        selectedStrategy,
        selectedStrategy === "manual" ? manualContent : undefined
      )
    })
  }

  const getConflictIcon = (type: ConflictData["conflictType"]) => {
    switch (type) {
      case "concurrent_edit":
        return <Icons.Users className="h-4 w-4 text-amber-500" />
      case "version_mismatch":
        return <Icons.GitBranch className="h-4 w-4 text-red-500" />
      case "edit_session_expired":
        return <Icons.Clock className="h-4 w-4 text-blue-500" />
      default:
        return <Icons.AlertCircle className="h-4 w-4 text-gray-500" />
    }
  }

  const getConflictDescription = (type: ConflictData["conflictType"]) => {
    switch (type) {
      case "concurrent_edit":
        return `Another user (${conflictData.conflictingUser?.name || "Unknown"}) is currently editing this deliverable.`
      case "version_mismatch":
        return `This deliverable was modified by another user while you were editing. Your version (${conflictData.clientVersion}) is behind the current version (${conflictData.serverVersion}).`
      case "edit_session_expired":
        return `Your editing session has expired. The deliverable may have been modified by another user.`
      default:
        return "A conflict has been detected with your changes."
    }
  }

  const getResolutionOptions = () => {
    const options = [
      {
        strategy: "user_merge" as const,
        title: "Smart Merge",
        description: "Automatically combine both versions where possible",
        icon: <Icons.Merge className="h-4 w-4" />,
        recommended: true,
      },
      {
        strategy: "client_wins" as const,
        title: "Keep My Version",
        description: "Use your version and overwrite server changes",
        icon: <Icons.User className="h-4 w-4" />,
        recommended: false,
      },
      {
        strategy: "server_wins" as const,
        title: "Keep Server Version",
        description: "Discard your changes and use the current server version",
        icon: <Icons.Server className="h-4 w-4" />,
        recommended: false,
      },
      {
        strategy: "manual" as const,
        title: "Manual Resolution",
        description: "Manually resolve conflicts by editing the content",
        icon: <Icons.Edit className="h-4 w-4" />,
        recommended: false,
      },
    ]

    return options
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center gap-2">
            {getConflictIcon(conflictData.conflictType)}
            <DialogTitle>Resolve Editing Conflict</DialogTitle>
          </div>
          <DialogDescription>
            {getConflictDescription(conflictData.conflictType)}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Conflict Details */}
          <Alert>
            <Icons.Info className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-1">
                <div>
                  <strong>Conflict Type:</strong>{" "}
                  {conflictData.conflictType.replace("_", " ")}
                </div>
                <div>
                  <strong>Your Version:</strong> {conflictData.clientVersion} |
                  <strong className="ml-2">Current Version:</strong>{" "}
                  {conflictData.serverVersion}
                </div>
                {conflictData.lastModified && (
                  <div>
                    <strong>Last Modified:</strong>{" "}
                    {formatDistanceToNow(conflictData.lastModified)} ago
                    {conflictData.conflictingUser && (
                      <span className="ml-2">
                        by {conflictData.conflictingUser.name}
                      </span>
                    )}
                  </div>
                )}
              </div>
            </AlertDescription>
          </Alert>

          {/* Resolution Strategy Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">
                Choose Resolution Strategy
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-3">
                {getResolutionOptions().map((option) => (
                  <div
                    key={option.strategy}
                    className={`
                      flex items-start gap-3 p-3 rounded-lg border cursor-pointer transition-colors
                      ${
                        selectedStrategy === option.strategy
                          ? "border-primary bg-primary/5"
                          : "border-border hover:bg-muted/50"
                      }
                    `}
                    onClick={() => setSelectedStrategy(option.strategy)}
                  >
                    <div className="mt-0.5">{option.icon}</div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{option.title}</span>
                        {option.recommended && (
                          <Badge variant="secondary" className="text-xs">
                            Recommended
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        {option.description}
                      </p>
                    </div>
                    <div className="mt-0.5">
                      <div
                        className={`
                        w-4 h-4 rounded-full border-2 
                        ${
                          selectedStrategy === option.strategy
                            ? "border-primary bg-primary"
                            : "border-muted-foreground"
                        }
                      `}
                      >
                        {selectedStrategy === option.strategy && (
                          <div className="w-full h-full rounded-full bg-background scale-50" />
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Content Comparison */}
          <Tabs defaultValue="comparison" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="comparison">Side-by-Side</TabsTrigger>
              <TabsTrigger value="your-version">Your Version</TabsTrigger>
              <TabsTrigger value="server-version">Server Version</TabsTrigger>
            </TabsList>

            <TabsContent value="comparison" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm flex items-center gap-2">
                      <Icons.User className="h-4 w-4" />
                      Your Version (v{conflictData.clientVersion})
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="max-h-60 overflow-y-auto p-3 bg-muted rounded border text-sm font-mono whitespace-pre-wrap">
                      {clientContent || "No content"}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm flex items-center gap-2">
                      <Icons.Server className="h-4 w-4" />
                      Server Version (v{conflictData.serverVersion})
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="max-h-60 overflow-y-auto p-3 bg-muted rounded border text-sm font-mono whitespace-pre-wrap">
                      {conflictData.conflictDetails?.serverContent ||
                        "Loading server content..."}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="your-version">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Your Version</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="max-h-80 overflow-y-auto p-3 bg-muted rounded border text-sm font-mono whitespace-pre-wrap">
                    {clientContent}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="server-version">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Server Version</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="max-h-80 overflow-y-auto p-3 bg-muted rounded border text-sm font-mono whitespace-pre-wrap">
                    {conflictData.conflictDetails?.serverContent ||
                      "Loading..."}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Manual Resolution Editor */}
          {selectedStrategy === "manual" && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Manual Resolution</CardTitle>
                <div className="text-xs text-muted-foreground">
                  Edit the content below to resolve the conflict manually.
                </div>
              </CardHeader>
              <CardContent>
                <Editor
                  value={
                    manualContent ||
                    conflictData.conflictDetails?.diff ||
                    clientContent
                  }
                  onValueChange={setManualContent}
                  placeholder="Manually resolve the conflict by editing this content..."
                />
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleResolve}
            disabled={isPending || isResolving}
            className="min-w-[120px]"
          >
            {(isPending || isResolving) && (
              <Icons.Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            Resolve Conflict
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
