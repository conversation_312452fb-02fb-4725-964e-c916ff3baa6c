"use client"

import * as React from "react"
import * as DialogPrimitive from "@radix-ui/react-dialog"
import { XIcon } from "lucide-react"

import { cn } from "@/lib/utils"

// Module augmentation for Dialog components
declare module "@radix-ui/react-dialog" {
  interface DialogTitleProps {
    children?: React.ReactNode
  }
  interface DialogDescriptionProps {
    children?: React.ReactNode
  }
}

const Dialog = React.forwardRef<
  React.ComponentRef<typeof DialogPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Root>
>(({ ...props }, ref) => {
  const RootComponent = DialogPrimitive.Root as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof DialogPrimitive.Root> & {
      ref?: React.Ref<HTMLDivElement>
    }
  >
  return <RootComponent data-slot="dialog" ref={ref} {...props} />
})
Dialog.displayName = "Dialog"

const DialogTrigger = React.forwardRef<
  React.ComponentRef<typeof DialogPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Trigger>
>(({ ...props }, ref) => {
  const TriggerComponent = DialogPrimitive.Trigger as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof DialogPrimitive.Trigger> & {
      ref?: React.Ref<HTMLButtonElement>
    }
  >
  return <TriggerComponent data-slot="dialog-trigger" ref={ref} {...props} />
})
DialogTrigger.displayName = "DialogTrigger"

const DialogPortal = React.forwardRef<
  React.ComponentRef<typeof DialogPrimitive.Portal>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Portal>
>(({ ...props }, ref) => {
  const PortalComponent = DialogPrimitive.Portal as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof DialogPrimitive.Portal> & {
      ref?: React.Ref<HTMLDivElement>
    }
  >
  return <PortalComponent data-slot="dialog-portal" ref={ref} {...props} />
})
DialogPortal.displayName = "DialogPortal"

const DialogClose = React.forwardRef<
  React.ComponentRef<typeof DialogPrimitive.Close>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Close>
>(({ ...props }, ref) => {
  const CloseComponent = DialogPrimitive.Close as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof DialogPrimitive.Close> & {
      ref?: React.Ref<HTMLButtonElement>
    }
  >
  return <CloseComponent data-slot="dialog-close" ref={ref} {...props} />
})
DialogClose.displayName = "DialogClose"

const DialogOverlay = React.forwardRef<
  React.ComponentRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay> & {
    className?: string
  }
>(({ className, ...props }, ref) => {
  const OverlayComponent = DialogPrimitive.Overlay as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay> & {
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <OverlayComponent
      data-slot="dialog-overlay"
      ref={ref}
      className={cn(
        "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",
        className
      )}
      {...props}
    />
  )
})
DialogOverlay.displayName = "DialogOverlay"

const DialogContent = React.forwardRef<
  React.ComponentRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content> & {
    className?: string
    children?: React.ReactNode
    showCloseButton?: boolean
  }
>(({ className, children, showCloseButton = true, ...props }, ref) => {
  const ContentComponent = DialogPrimitive.Content as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content> & {
      className?: string
      children?: React.ReactNode
      ref?: React.Ref<HTMLDivElement>
    }
  >

  const CloseComponent = DialogPrimitive.Close as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof DialogPrimitive.Close> & {
      className?: string
      children?: React.ReactNode
    }
  >

  return (
    <DialogPortal data-slot="dialog-portal">
      <DialogOverlay />
      <ContentComponent
        data-slot="dialog-content"
        ref={ref}
        className={cn(
          "bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",
          className
        )}
        {...props}
      >
        {children}
        {showCloseButton && (
          <CloseComponent
            data-slot="dialog-close"
            className="ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4"
          >
            <XIcon />
            <span className="sr-only">Close</span>
          </CloseComponent>
        )}
      </ContentComponent>
    </DialogPortal>
  )
})
DialogContent.displayName = "DialogContent"

const DialogHeader = React.forwardRef<
  HTMLDivElement,
  React.ComponentPropsWithoutRef<"div"> & {
    className?: string
  }
>(({ className, ...props }, ref) => {
  return (
    <div
      data-slot="dialog-header"
      ref={ref}
      className={cn("flex flex-col gap-2 text-center sm:text-left", className)}
      {...props}
    />
  )
})
DialogHeader.displayName = "DialogHeader"

const DialogFooter = React.forwardRef<
  HTMLDivElement,
  React.ComponentPropsWithoutRef<"div"> & {
    className?: string
  }
>(({ className, ...props }, ref) => {
  return (
    <div
      data-slot="dialog-footer"
      ref={ref}
      className={cn(
        "flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",
        className
      )}
      {...props}
    />
  )
})
DialogFooter.displayName = "DialogFooter"

const DialogTitle = React.forwardRef<
  React.ComponentRef<typeof DialogPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title> & {
    className?: string
    children?: React.ReactNode
  }
>(({ className, ...props }, ref) => {
  const TitleComponent = DialogPrimitive.Title as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title> & {
      className?: string
      children?: React.ReactNode
      ref?: React.Ref<HTMLHeadingElement>
    }
  >

  return (
    <TitleComponent
      data-slot="dialog-title"
      ref={ref}
      className={cn("text-lg leading-none font-semibold", className)}
      {...props}
    />
  )
})
DialogTitle.displayName = "DialogTitle"

const DialogDescription = React.forwardRef<
  React.ComponentRef<typeof DialogPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description> & {
    className?: string
    children?: React.ReactNode
  }
>(({ className, ...props }, ref) => {
  const DescriptionComponent = DialogPrimitive.Description as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description> & {
      className?: string
      children?: React.ReactNode
      ref?: React.Ref<HTMLParagraphElement>
    }
  >

  return (
    <DescriptionComponent
      data-slot="dialog-description"
      ref={ref}
      className={cn("text-muted-foreground text-sm", className)}
      {...props}
    />
  )
})
DialogDescription.displayName = "DialogDescription"

export {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  DialogTrigger,
}
