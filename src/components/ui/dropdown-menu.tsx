"use client"

import * as React from "react"
import * as DropdownMenuPrimitive from "@radix-ui/react-dropdown-menu"
import { CheckIcon, ChevronRightIcon, CircleIcon } from "lucide-react"

import { cn } from "@/lib/utils"

// Module augmentation for DropdownMenuItemIndicator
declare module "@radix-ui/react-dropdown-menu" {
  interface DropdownMenuItemIndicatorProps {
    children?: React.ReactNode
  }
}

const DropdownMenu = React.forwardRef<
  React.ComponentRef<typeof DropdownMenuPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Root>
>(({ ...props }, ref) => {
  const RootComponent = DropdownMenuPrimitive.Root as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Root> & {
      ref?: React.Ref<HTMLDivElement>
    }
  >
  return <RootComponent data-slot="dropdown-menu" ref={ref} {...props} />
})
DropdownMenu.displayName = "DropdownMenu"

const DropdownMenuPortal = React.forwardRef<
  React.ComponentRef<typeof DropdownMenuPrimitive.Portal>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Portal>
>(({ ...props }, ref) => {
  const PortalComponent = DropdownMenuPrimitive.Portal as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Portal> & {
      ref?: React.Ref<HTMLDivElement>
    }
  >
  return (
    <PortalComponent data-slot="dropdown-menu-portal" ref={ref} {...props} />
  )
})
DropdownMenuPortal.displayName = "DropdownMenuPortal"

const DropdownMenuTrigger = React.forwardRef<
  React.ComponentRef<typeof DropdownMenuPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Trigger>
>(({ ...props }, ref) => {
  const TriggerComponent = DropdownMenuPrimitive.Trigger as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Trigger> & {
      ref?: React.Ref<HTMLButtonElement>
    }
  >
  return (
    <TriggerComponent
      data-slot="dropdown-menu-trigger"
      ref={ref}
      {...props}
    />
  )
})
DropdownMenuTrigger.displayName = "DropdownMenuTrigger"

const DropdownMenuContent = React.forwardRef<
  React.ComponentRef<typeof DropdownMenuPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content> & {
    className?: string
    sideOffset?: number
    children?: React.ReactNode
  }
>(({ className, sideOffset = 4, ...props }, ref) => {
  const ContentComponent = DropdownMenuPrimitive.Content as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content> & {
      className?: string
      children?: React.ReactNode
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <DropdownMenuPrimitive.Portal>
      <ContentComponent
        data-slot="dropdown-menu-content"
        ref={ref}
        sideOffset={sideOffset}
        className={cn(
          "bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",
          className
        )}
        {...props}
      />
    </DropdownMenuPrimitive.Portal>
  )
})
DropdownMenuContent.displayName = "DropdownMenuContent"

const DropdownMenuGroup = React.forwardRef<
  React.ComponentRef<typeof DropdownMenuPrimitive.Group>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Group> & {
    children?: React.ReactNode
  }
>(({ ...props }, ref) => {
  const GroupComponent = DropdownMenuPrimitive.Group as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Group> & {
      children?: React.ReactNode
      ref?: React.Ref<HTMLDivElement>
    }
  >
  return (
    <GroupComponent data-slot="dropdown-menu-group" ref={ref} {...props} />
  )
})
DropdownMenuGroup.displayName = "DropdownMenuGroup"

const DropdownMenuItem = React.forwardRef<
  React.ComponentRef<typeof DropdownMenuPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {
    className?: string
    inset?: boolean
    variant?: "default" | "destructive"
    children?: React.ReactNode
  }
>(({ className, inset, variant = "default", ...props }, ref) => {
  const ItemComponent = DropdownMenuPrimitive.Item as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {
      className?: string
      children?: React.ReactNode
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <ItemComponent
      data-slot="dropdown-menu-item"
      data-inset={inset}
      data-variant={variant}
      ref={ref}
      className={cn(
        "focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
        className
      )}
      {...props}
    />
  )
})
DropdownMenuItem.displayName = "DropdownMenuItem"

const DropdownMenuCheckboxItem = React.forwardRef<
  React.ComponentRef<typeof DropdownMenuPrimitive.CheckboxItem>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem> & {
    className?: string
    children?: React.ReactNode
  }
>(({ className, children, checked, ...props }, ref) => {
  const CheckboxItemComponent = DropdownMenuPrimitive.CheckboxItem as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem> & {
      className?: string
      children?: React.ReactNode
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <CheckboxItemComponent
      data-slot="dropdown-menu-checkbox-item"
      ref={ref}
      className={cn(
        "focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
        className
      )}
      checked={checked}
      {...props}
    >
      <span className="pointer-events-none absolute left-2 flex size-3.5 items-center justify-center">
        <DropdownMenuPrimitive.ItemIndicator>
          <CheckIcon className="size-4" />
        </DropdownMenuPrimitive.ItemIndicator>
      </span>
      {children}
    </CheckboxItemComponent>
  )
})
DropdownMenuCheckboxItem.displayName = "DropdownMenuCheckboxItem"

const DropdownMenuRadioGroup = React.forwardRef<
  React.ComponentRef<typeof DropdownMenuPrimitive.RadioGroup>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioGroup> & {
    children?: React.ReactNode
  }
>(({ ...props }, ref) => {
  const RadioGroupComponent = DropdownMenuPrimitive.RadioGroup as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioGroup> & {
      children?: React.ReactNode
      ref?: React.Ref<HTMLDivElement>
    }
  >
  return (
    <RadioGroupComponent
      data-slot="dropdown-menu-radio-group"
      ref={ref}
      {...props}
    />
  )
})
DropdownMenuRadioGroup.displayName = "DropdownMenuRadioGroup"

const DropdownMenuRadioItem = React.forwardRef<
  React.ComponentRef<typeof DropdownMenuPrimitive.RadioItem>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem> & {
    className?: string
    children?: React.ReactNode
  }
>(({ className, children, ...props }, ref) => {
  const RadioItemComponent = DropdownMenuPrimitive.RadioItem as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem> & {
      className?: string
      children?: React.ReactNode
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <RadioItemComponent
      data-slot="dropdown-menu-radio-item"
      ref={ref}
      className={cn(
        "focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
        className
      )}
      {...props}
    >
      <span className="pointer-events-none absolute left-2 flex size-3.5 items-center justify-center">
        <DropdownMenuPrimitive.ItemIndicator>
          <CircleIcon className="size-2 fill-current" />
        </DropdownMenuPrimitive.ItemIndicator>
      </span>
      {children}
    </RadioItemComponent>
  )
})
DropdownMenuRadioItem.displayName = "DropdownMenuRadioItem"

const DropdownMenuLabel = React.forwardRef<
  React.ComponentRef<typeof DropdownMenuPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {
    className?: string
    inset?: boolean
    children?: React.ReactNode
  }
>(({ className, inset, ...props }, ref) => {
  const LabelComponent = DropdownMenuPrimitive.Label as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {
      className?: string
      children?: React.ReactNode
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <LabelComponent
      data-slot="dropdown-menu-label"
      data-inset={inset}
      ref={ref}
      className={cn(
        "px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",
        className
      )}
      {...props}
    />
  )
})
DropdownMenuLabel.displayName = "DropdownMenuLabel"

const DropdownMenuSeparator = React.forwardRef<
  React.ComponentRef<typeof DropdownMenuPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator> & {
    className?: string
  }
>(({ className, ...props }, ref) => {
  const SeparatorComponent = DropdownMenuPrimitive.Separator as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator> & {
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <SeparatorComponent
      data-slot="dropdown-menu-separator"
      ref={ref}
      className={cn("bg-border -mx-1 my-1 h-px", className)}
      {...props}
    />
  )
})
DropdownMenuSeparator.displayName = "DropdownMenuSeparator"

const DropdownMenuShortcut = React.forwardRef<
  HTMLSpanElement,
  React.ComponentPropsWithoutRef<"span"> & {
    className?: string
  }
>(({ className, ...props }, ref) => {
  return (
    <span
      data-slot="dropdown-menu-shortcut"
      ref={ref}
      className={cn(
        "text-muted-foreground ml-auto text-xs tracking-widest",
        className
      )}
      {...props}
    />
  )
})
DropdownMenuShortcut.displayName = "DropdownMenuShortcut"

const DropdownMenuSub = React.forwardRef<
  React.ComponentRef<typeof DropdownMenuPrimitive.Sub>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Sub>
>(({ ...props }, ref) => {
  const SubComponent = DropdownMenuPrimitive.Sub as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Sub> & {
      ref?: React.Ref<HTMLDivElement>
    }
  >
  return <SubComponent data-slot="dropdown-menu-sub" ref={ref} {...props} />
})
DropdownMenuSub.displayName = "DropdownMenuSub"

const DropdownMenuSubTrigger = React.forwardRef<
  React.ComponentRef<typeof DropdownMenuPrimitive.SubTrigger>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {
    className?: string
    inset?: boolean
    children?: React.ReactNode
  }
>(({ className, inset, children, ...props }, ref) => {
  const SubTriggerComponent = DropdownMenuPrimitive.SubTrigger as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {
      className?: string
      children?: React.ReactNode
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <SubTriggerComponent
      data-slot="dropdown-menu-sub-trigger"
      data-inset={inset}
      ref={ref}
      className={cn(
        "focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8",
        className
      )}
      {...props}
    >
      {children}
      <ChevronRightIcon className="ml-auto size-4" />
    </SubTriggerComponent>
  )
})
DropdownMenuSubTrigger.displayName = "DropdownMenuSubTrigger"

const DropdownMenuSubContent = React.forwardRef<
  React.ComponentRef<typeof DropdownMenuPrimitive.SubContent>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent> & {
    className?: string
  }
>(({ className, ...props }, ref) => {
  const SubContentComponent = DropdownMenuPrimitive.SubContent as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent> & {
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <SubContentComponent
      data-slot="dropdown-menu-sub-content"
      ref={ref}
      className={cn(
        "bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg",
        className
      )}
      {...props}
    />
  )
})
DropdownMenuSubContent.displayName = "DropdownMenuSubContent"

export {
  DropdownMenu,
  DropdownMenuPortal,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuLabel,
  DropdownMenuItem,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent,
}
