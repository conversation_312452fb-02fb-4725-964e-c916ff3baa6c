// import type { PDFDocumentProxy } from "pdfjs-dist" // TODO: Install pdfjs-dist when implementing OCR

import { SupportService } from "@/lib/services/support-service"

/**
 * OCR Service for extracting grades from PDF transcripts
 * Integrates with multiple OCR providers with fallback support:
 * - Primary: Tesseract.js (open source)
 * - Secondary: Google Cloud Vision API (if primary fails)
 * - Manual review ticket creation on all failures
 */

interface GradeData {
  courseName: string
  courseCode: string
  grade: string
  gradePoints?: number // Out of 40 scale
  credits: number
  term: string
  year: number
}

// Grade conversion from letter to 40-point scale
const GRADE_TO_40_SCALE: Record<string, number> = {
  "A+": 40,
  A: 38,
  "A-": 35,
  "B+": 32,
  B: 30,
  "B-": 28,
  "C+": 25,
  C: 22,
  "C-": 20,
  "D+": 18,
  D: 15,
  "D-": 12,
  F: 0,
}

interface TranscriptData {
  studentName: string
  studentId: string
  program: string
  gpa: number
  totalCredits: number
  grades: GradeData[]
}

// Primary OCR using Tesseract.js (stubbed for now)
async function primaryOCR(pdfUrl: string): Promise<string | null> {
  // In production: Use Tesseract.js or pdf-parse
  // For now, return null to simulate failure
  return null
}

// Secondary OCR using Google Cloud Vision (stubbed for now)
async function secondaryOCR(pdfUrl: string): Promise<string | null> {
  // In production: Use Google Cloud Vision API
  // For now, return null to simulate failure
  return null
}

/**
 * Extract grade data from a PDF with fallback mechanisms
 * 1. Try primary OCR (Tesseract.js)
 * 2. If fails, try secondary OCR (Google Cloud Vision)
 * 3. If both fail, create support ticket for manual review
 */
export async function extractGradesFromPDF(
  pdfUrl: string,
  userId?: string,
  fileName?: string
): Promise<TranscriptData> {
  let extractedText: string | null = null
  let lastError: string = "Unknown error"

  // Try primary OCR
  try {
    extractedText = await primaryOCR(pdfUrl)
    if (!extractedText) {
      lastError = "Primary OCR returned empty result"
    }
  } catch (error) {
    lastError = `Primary OCR failed: ${error instanceof Error ? error.message : "Unknown error"}`
    console.error("Primary OCR error:", error)
  }

  // Try secondary OCR if primary failed
  if (!extractedText) {
    try {
      extractedText = await secondaryOCR(pdfUrl)
      if (!extractedText) {
        lastError = "Secondary OCR returned empty result"
      }
    } catch (error) {
      lastError = `Secondary OCR failed: ${error instanceof Error ? error.message : "Unknown error"}`
      console.error("Secondary OCR error:", error)
    }
  }

  // If both OCR methods failed, create support ticket
  if (!extractedText) {
    if (userId && fileName) {
      await SupportService.createPDFParseFailureTicket(
        userId,
        fileName,
        lastError,
        pdfUrl
      )
    }

    // Throw error to indicate failure
    throw new Error(
      `Failed to parse PDF: ${lastError}. A support ticket has been created for manual review.`
    )
  }

  // Parse the extracted text (for now, return mock data)
  // In production, implement proper text parsing logic
  const mockData: TranscriptData = {
    studentName: "John Doe",
    studentId: "250123456",
    program: "JD/MBA",
    gpa: 3.75,
    totalCredits: 45,
    grades: [
      {
        courseName: "Constitutional Law",
        courseCode: "LAW 5001",
        grade: "A-",
        gradePoints: GRADE_TO_40_SCALE["A-"],
        credits: 3,
        term: "Fall",
        year: 2023,
      },
      {
        courseName: "Contracts",
        courseCode: "LAW 5002",
        grade: "B+",
        gradePoints: GRADE_TO_40_SCALE["B+"],
        credits: 3,
        term: "Fall",
        year: 2023,
      },
      {
        courseName: "Criminal Law",
        courseCode: "LAW 5003",
        grade: "A",
        gradePoints: GRADE_TO_40_SCALE["A"],
        credits: 3,
        term: "Winter",
        year: 2024,
      },
      {
        courseName: "Business Ethics",
        courseCode: "MBA 6001",
        grade: "A-",
        gradePoints: GRADE_TO_40_SCALE["A-"],
        credits: 3,
        term: "Fall",
        year: 2023,
      },
      {
        courseName: "Financial Management",
        courseCode: "MBA 6002",
        grade: "B+",
        gradePoints: GRADE_TO_40_SCALE["B+"],
        credits: 3,
        term: "Winter",
        year: 2024,
      },
    ],
  }

  return mockData
}

/**
 * Calculate GPA from letter grades (traditional 4.0 scale)
 * @param grades Array of letter grades
 * @returns Numeric GPA
 */
export function calculateGPA(grades: string[]): number {
  const gradePoints: Record<string, number> = {
    "A+": 4.0,
    A: 4.0,
    "A-": 3.7,
    "B+": 3.3,
    B: 3.0,
    "B-": 2.7,
    "C+": 2.3,
    C: 2.0,
    "C-": 1.7,
    "D+": 1.3,
    D: 1.0,
    F: 0.0,
  }

  const points = grades
    .map((grade) => gradePoints[grade] || 0)
    .reduce((sum, point) => sum + point, 0)

  return grades.length > 0 ? points / grades.length : 0
}

/**
 * Calculate average on 40-point scale
 * @param grades Array of GradeData
 * @returns Average on 40-point scale
 */
export function calculate40PointAverage(grades: GradeData[]): number {
  const validGrades = grades.filter((g) => g.gradePoints !== undefined)
  if (validGrades.length === 0) return 0

  const totalPoints = validGrades.reduce(
    (sum, grade) => sum + (grade.gradePoints || 0),
    0
  )
  return totalPoints / validGrades.length
}

/**
 * Convert letter grade to 40-point scale
 */
export function convertTo40Scale(letterGrade: string): number {
  return GRADE_TO_40_SCALE[letterGrade] || 0
}

/**
 * Validate transcript data
 * @param data Transcript data to validate
 * @returns Validation result
 */
export function validateTranscriptData(data: TranscriptData): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []

  if (!data.studentName || data.studentName.trim().length === 0) {
    errors.push("Student name is missing")
  }

  if (!data.studentId || !/^\d{9}$/.test(data.studentId)) {
    errors.push("Invalid student ID format")
  }

  if (data.gpa < 0 || data.gpa > 4.0) {
    errors.push("GPA must be between 0 and 4.0")
  }

  if (data.totalCredits < 0) {
    errors.push("Total credits cannot be negative")
  }

  if (!data.grades || data.grades.length === 0) {
    errors.push("No grades found in transcript")
  }

  // Check for anomalies - 5 or more A grades
  const aGrades = data.grades.filter((g) => g.grade.startsWith("A")).length
  if (aGrades >= 5) {
    errors.push(
      `Anomaly detected: ${aGrades} A-level grades found. Manual review required.`
    )
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}

/**
 * Mock function to process grade submission PDF
 * This would be called when a student uploads their transcript
 */
export async function processGradeSubmission(
  fileUrl: string,
  studentProfileId: string,
  userId?: string,
  fileName?: string
): Promise<{
  success: boolean
  data?: TranscriptData
  error?: string
}> {
  try {
    // Extract data from PDF with fallback support
    const transcriptData = await extractGradesFromPDF(fileUrl, userId, fileName)

    // Validate the data
    const validation = validateTranscriptData(transcriptData)
    if (!validation.isValid) {
      return {
        success: false,
        error: `Invalid transcript data: ${validation.errors.join(", ")}`,
      }
    }

    // In production, you would:
    // 1. Save the extracted data to the database
    // 2. Update the student profile with GPA and grade information
    // 3. Trigger any necessary notifications

    return {
      success: true,
      data: transcriptData,
    }
  } catch (error) {
    console.error("Error processing grade submission:", error)
    return {
      success: false,
      error: "Failed to process transcript",
    }
  }
}
