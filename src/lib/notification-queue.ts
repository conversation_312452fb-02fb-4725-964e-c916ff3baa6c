import type { Job } from "bullmq"
import { Queue, Worker, QueueEvents } from "bullmq"
import { eq } from "drizzle-orm"

import { db } from "@/drizzle/db"
import { files } from "@/drizzle/schema"
import {
  sendApplicationConfirmation,
  sendDeliverableReminder,
  sendFacultyMatchNotification,
  sendInterviewInvitation,
  sendOrganizationMatchNotification,
  sendStudentMatchNotification,
  sendSupportTicketNotification,
} from "@/lib/email-service"
import { getRedisConnection } from "@/lib/redis-service"
import type { Result } from "@/lib/result"
import { err, fromPromise, ok } from "@/lib/result"
import { getScanResults } from "@/lib/virustotal-service"

// Job type definitions
export interface EmailJobData {
  type:
    | "application_confirmation"
    | "student_match"
    | "faculty_match"
    | "organization_match"
    | "interview_invitation"
    | "deliverable_reminder"
    | "support_ticket"
    | "virus_scan_check"
    | "virus_scan_failed"
    | "virus_scan_clean"
    | "virus_scan_infected"
  to: string
  data: Record<string, unknown>
  priority?: "low" | "normal" | "high" | "critical"
  delay?: number
  retryAttempts?: number
}

export interface NotificationJobResult {
  success: boolean
  emailId?: string
  error?: string
  timestamp: string
}

// Queue configuration
const QUEUE_NAME = "notifications"
const QUEUE_OPTIONS = {
  defaultJobOptions: {
    attempts: 3,
    backoff: {
      type: "exponential" as const,
      delay: 2000,
    },
    removeOnComplete: 100, // Keep last 100 completed jobs
    removeOnFail: 50, // Keep last 50 failed jobs
  },
}

// Global queue and worker instances
let notificationQueue: Queue<EmailJobData, NotificationJobResult> | null = null
let notificationWorker: Worker<EmailJobData, NotificationJobResult> | null =
  null
let queueEvents: QueueEvents | null = null

/**
 * Initialize the notification queue
 */
export function initializeNotificationQueue(): Result<
  Queue<EmailJobData, NotificationJobResult>,
  Error
> {
  // Get Redis connection
  const connectionResult = getRedisConnection()
  if (!connectionResult.success) {
    return err(
      new Error(
        `Failed to get Redis connection: ${connectionResult.error.message}`
      )
    )
  }

  const connection = connectionResult.data

  try {
    // Create queue if it doesn't exist
    if (!notificationQueue) {
      notificationQueue = new Queue<EmailJobData, NotificationJobResult>(
        QUEUE_NAME,
        {
          connection,
          ...QUEUE_OPTIONS,
        }
      )

      // Attach error handler
      notificationQueue.on("error", (error) => {
        console.error("Notification queue error:", error.message)
      })

      console.log("Notification queue initialized successfully")
    }

    return ok(notificationQueue)
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    return err(
      new Error(`Failed to initialize notification queue: ${errorMessage}`)
    )
  }
}

/**
 * Initialize the notification worker
 */
export function initializeNotificationWorker(): Result<
  Worker<EmailJobData, NotificationJobResult>,
  Error
> {
  // Get Redis connection
  const connectionResult = getRedisConnection()
  if (!connectionResult.success) {
    return err(
      new Error(
        `Failed to get Redis connection: ${connectionResult.error.message}`
      )
    )
  }

  const connection = connectionResult.data

  try {
    // Create worker if it doesn't exist
    if (!notificationWorker) {
      notificationWorker = new Worker<EmailJobData, NotificationJobResult>(
        QUEUE_NAME,
        processNotificationJob,
        {
          connection,
          concurrency: 5, // Process up to 5 jobs concurrently
          settings: {
            backoffStrategy: (
              attemptsMade: number,
              type: string,
              err: Error
            ) => {
              // Custom backoff for email failures
              if (err.message.includes("rate limit")) {
                return attemptsMade * 5000 // Longer delay for rate limits
              }
              return attemptsMade * 2000 // Standard exponential backoff
            },
          },
        }
      )

      // Attach event handlers
      notificationWorker.on(
        "completed",
        (
          job: Job<EmailJobData, NotificationJobResult>,
          result: NotificationJobResult
        ) => {
          console.log(`Notification job ${job.id} completed successfully:`, {
            type: job.data.type,
            to: job.data.to,
            emailId: result.emailId,
          })
        }
      )

      notificationWorker.on(
        "failed",
        (
          job: Job<EmailJobData, NotificationJobResult> | undefined,
          error: Error
        ) => {
          console.error(`Notification job ${job?.id} failed:`, {
            type: job?.data.type,
            to: job?.data.to,
            error: error.message,
            attempts: job?.attemptsMade,
          })
        }
      )

      notificationWorker.on("error", (error) => {
        console.error("Notification worker error:", error.message)
      })

      notificationWorker.on(
        "progress",
        (
          job: Job<EmailJobData, NotificationJobResult>,
          progress: number | object
        ) => {
          console.log(`Notification job ${job.id} progress:`, progress)
        }
      )

      console.log("Notification worker initialized successfully")
    }

    return ok(notificationWorker)
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    return err(
      new Error(`Failed to initialize notification worker: ${errorMessage}`)
    )
  }
}

/**
 * Initialize queue events listener for monitoring
 */
export function initializeQueueEvents(): Result<QueueEvents, Error> {
  // Get Redis connection
  const connectionResult = getRedisConnection()
  if (!connectionResult.success) {
    return err(
      new Error(
        `Failed to get Redis connection: ${connectionResult.error.message}`
      )
    )
  }

  const connection = connectionResult.data

  try {
    if (!queueEvents) {
      queueEvents = new QueueEvents(QUEUE_NAME, { connection })

      // Global event listeners for monitoring
      queueEvents.on("completed", ({ jobId, returnvalue }) => {
        console.log(`Global: Job ${jobId} completed with result:`, returnvalue)
      })

      queueEvents.on("failed", ({ jobId, failedReason }) => {
        console.error(`Global: Job ${jobId} failed:`, failedReason)
      })

      queueEvents.on("progress", ({ jobId, data }) => {
        console.log(`Global: Job ${jobId} progress:`, data)
      })

      console.log("Queue events listener initialized successfully")
    }

    return ok(queueEvents)
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    return err(new Error(`Failed to initialize queue events: ${errorMessage}`))
  }
}

/**
 * Process notification jobs
 */
async function processNotificationJob(
  job: Job<EmailJobData, NotificationJobResult>
): Promise<NotificationJobResult> {
  const { type, to, data } = job.data
  const timestamp = new Date().toISOString()

  // Update progress
  await job.updateProgress({ status: "processing", timestamp })

  try {
    let emailResult: Result<{ id: string }, Error>

    switch (type) {
      case "application_confirmation":
        emailResult = await sendApplicationConfirmation(
          to,
          data.studentName as string
        )
        break

      case "student_match":
        emailResult = await sendStudentMatchNotification(
          to,
          data.studentName as string,
          data.matchType as "organization" | "faculty",
          data.matchName as string,
          data.nextSteps as string
        )
        break

      case "faculty_match":
        emailResult = await sendFacultyMatchNotification(
          to,
          data.facultyName as string,
          data.studentName as string,
          data.projectTitle as string
        )
        break

      case "organization_match":
        emailResult = await sendOrganizationMatchNotification(
          to,
          data.contactName as string,
          data.students as Array<{ name: string; programme: string }>,
          data.organizationName as string
        )
        break

      case "interview_invitation":
        emailResult = await sendInterviewInvitation(
          to,
          data.studentName as string,
          data.organizationName as string,
          new Date(data.interviewDate as string),
          data.interviewLocation as string,
          data.interviewType as "virtual" | "in-person",
          data.meetingLink as string | undefined,
          data.icsAttachment as Buffer | undefined
        )
        break

      case "deliverable_reminder":
        emailResult = await sendDeliverableReminder(
          to,
          data.studentName as string,
          data.deliverableName as string,
          new Date(data.dueDate as string),
          data.daysUntilDue as number
        )
        break

      case "support_ticket":
        emailResult = await sendSupportTicketNotification(
          to,
          data.adminName as string,
          data.ticketType as string,
          data.ticketDetails as string,
          data.studentInfo as { name: string; email: string } | undefined
        )
        break

      case "virus_scan_check":
        // Check VirusTotal scan results and update database
        const scanId = data.scanId as string
        const fileId = data.fileId as string
        const fileName = data.fileName as string

        await job.updateProgress({
          status: "checking_virus_scan",
          timestamp: new Date().toISOString(),
        })

        const scanResult = await getScanResults(scanId)
        if (scanResult.success) {
          const virusResults = scanResult.data

          // Update file record with scan results
          const updateResult = await Promise.resolve(
            db
              .update(files)
              .set({
                virusScanStatus: virusResults.isClean ? "clean" : "infected",
                virusScanDate: virusResults.scanDate,
                virusThreatCount: virusResults.threatCount,
                virusEnginesCount: virusResults.totalEngines,
                virusScanDetails: virusResults.threats,
                virusPermalink: virusResults.permalink,
                updatedAt: new Date(),
              })
              .where(eq(files.id, fileId))
          ).then(
            () => ({ success: true as const }),
            (error) => ({ success: false as const, error })
          )

          if (updateResult.success) {
            // Queue appropriate notification based on scan results
            if (virusResults.isClean) {
              // File is clean - no notification needed for normal cases
              emailResult = ok({ id: `virus-clean-${fileId}` })
            } else {
              // File is infected - create urgent notification job directly
              if (notificationQueue) {
                await notificationQueue.add(
                  "virus_scan_infected",
                  {
                    type: "virus_scan_infected",
                    to: process.env.ADMIN_EMAIL || "<EMAIL>",
                    data: {
                      fileId,
                      fileName,
                      userId: data.userId,
                      threatCount: virusResults.threatCount,
                      threats: virusResults.threats,
                      permalink: virusResults.permalink,
                    },
                    priority: "critical",
                  },
                  { priority: 1 }
                )
              }
              emailResult = ok({ id: `virus-infected-${fileId}` })
            }
          } else {
            throw new Error(
              `Failed to update file scan results: ${updateResult.error}`
            )
          }
        } else if (
          scanResult.error.message.includes("queued") ||
          scanResult.error.message.includes("scanning")
        ) {
          // Scan still in progress - reschedule this job with delay
          if (notificationQueue) {
            await notificationQueue.add(
              "virus_scan_check",
              {
                type: "virus_scan_check",
                to,
                data,
                priority: "normal",
              },
              {
                delay: 30000, // Check again in 30 seconds
                priority: 5,
              }
            )
          }
          emailResult = ok({ id: `virus-check-rescheduled-${fileId}` })
        } else {
          throw new Error(
            `VirusTotal scan check failed: ${scanResult.error.message}`
          )
        }
        break

      case "virus_scan_failed":
        // Notify admin about virus scanning failure
        emailResult = ok({ id: `virus-scan-failed-${data.fileId}` }) // Placeholder - add real email template
        break

      case "virus_scan_clean":
        // File passed virus scan - optional notification
        emailResult = ok({ id: `virus-scan-clean-${data.fileId}` }) // Placeholder - add real email template
        break

      case "virus_scan_infected":
        // File failed virus scan - urgent notification
        emailResult = ok({ id: `virus-scan-infected-${data.fileId}` }) // Placeholder - add real email template
        break

      default:
        throw new Error(`Unknown notification type: ${type}`)
    }

    // Update progress
    await job.updateProgress({
      status: "email_sent",
      timestamp: new Date().toISOString(),
    })

    if (!emailResult.success) {
      throw new Error(`Email sending failed: ${emailResult.error.message}`)
    }

    return {
      success: true,
      emailId: emailResult.data.id,
      timestamp,
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)

    // Update progress with error
    await job.updateProgress({
      status: "failed",
      error: errorMessage,
      timestamp: new Date().toISOString(),
    })

    return {
      success: false,
      error: errorMessage,
      timestamp,
    }
  }
}

/**
 * Add a notification job to the queue
 */
export async function addNotificationJob(
  jobData: EmailJobData
): Promise<Result<Job<EmailJobData, NotificationJobResult>, Error>> {
  // Initialize queue if needed
  const queueResult = initializeNotificationQueue()
  if (!queueResult.success) {
    return err(queueResult.error)
  }

  const queue = queueResult.data

  // Validate job data
  if (!jobData.to || jobData.to.trim() === "") {
    return err(new Error("Recipient email address is required"))
  }

  if (!jobData.type) {
    return err(new Error("Notification type is required"))
  }

  // Set priority and options based on job type
  const getPriority = (type: EmailJobData["type"]) => {
    switch (type) {
      case "support_ticket":
        return 1 // Highest priority
      case "interview_invitation":
        return 2
      case "student_match":
      case "faculty_match":
      case "organization_match":
        return 3
      case "deliverable_reminder":
        return 4
      case "application_confirmation":
        return 5 // Lowest priority
      default:
        return 5
    }
  }

  const jobOptions = {
    priority: getPriority(jobData.type),
    delay: jobData.delay || 0,
    attempts: jobData.retryAttempts || 3,
    removeOnComplete: 100,
    removeOnFail: 50,
  }

  // Add job to queue
  const addJobResult = await fromPromise(
    queue.add(`${jobData.type}-${jobData.to}`, jobData, jobOptions)
  )

  if (!addJobResult.success) {
    return err(
      new Error(`Failed to add notification job: ${addJobResult.error.message}`)
    )
  }

  console.log(
    `Notification job added to queue: ${jobData.type} for ${jobData.to}`
  )
  return ok(addJobResult.data)
}

/**
 * Get queue statistics
 */
export async function getQueueStatistics(): Promise<
  Result<
    {
      waiting: number
      active: number
      completed: number
      failed: number
      delayed: number
    },
    Error
  >
> {
  const queueResult = initializeNotificationQueue()
  if (!queueResult.success) {
    return err(queueResult.error)
  }

  const queue = queueResult.data

  const statsResult = await fromPromise(
    Promise.all([
      queue.getWaiting(),
      queue.getActive(),
      queue.getCompleted(),
      queue.getFailed(),
      queue.getDelayed(),
    ])
  )

  if (!statsResult.success) {
    return err(
      new Error(`Failed to get queue statistics: ${statsResult.error.message}`)
    )
  }

  const [waiting, active, completed, failed, delayed] = statsResult.data

  return ok({
    waiting: waiting.length,
    active: active.length,
    completed: completed.length,
    failed: failed.length,
    delayed: delayed.length,
  })
}

/**
 * Pause the queue
 */
export async function pauseQueue(): Promise<Result<void, Error>> {
  const queueResult = initializeNotificationQueue()
  if (!queueResult.success) {
    return err(queueResult.error)
  }

  const queue = queueResult.data
  const pauseResult = await fromPromise(queue.pause())

  if (!pauseResult.success) {
    return err(new Error(`Failed to pause queue: ${pauseResult.error.message}`))
  }

  console.log("Notification queue paused")
  return ok(undefined)
}

/**
 * Resume the queue
 */
export async function resumeQueue(): Promise<Result<void, Error>> {
  const queueResult = initializeNotificationQueue()
  if (!queueResult.success) {
    return err(queueResult.error)
  }

  const queue = queueResult.data
  const resumeResult = await fromPromise(queue.resume())

  if (!resumeResult.success) {
    return err(
      new Error(`Failed to resume queue: ${resumeResult.error.message}`)
    )
  }

  console.log("Notification queue resumed")
  return ok(undefined)
}

/**
 * Clean up old jobs
 */
export async function cleanQueue(
  maxAge: number = 24 * 60 * 60 * 1000, // 24 hours in milliseconds
  limit: number = 1000
): Promise<Result<number[], Error>> {
  const queueResult = initializeNotificationQueue()
  if (!queueResult.success) {
    return err(queueResult.error)
  }

  const queue = queueResult.data

  const cleanResult = await fromPromise(
    Promise.all([
      queue.clean(maxAge, limit, "completed"),
      queue.clean(maxAge, limit, "failed"),
    ])
  )

  if (!cleanResult.success) {
    return err(new Error(`Failed to clean queue: ${cleanResult.error.message}`))
  }

  console.log(
    `Queue cleaned: removed ${cleanResult.data[0].length} completed and ${cleanResult.data[1].length} failed jobs`
  )
  return ok(cleanResult.data)
}

/**
 * Gracefully shut down queue and worker
 */
export async function shutdownNotificationSystem(): Promise<
  Result<void, Error>
> {
  const errors: string[] = []

  // Close worker
  if (notificationWorker) {
    const workerResult = await fromPromise(notificationWorker.close())
    if (!workerResult.success) {
      errors.push(`Worker shutdown error: ${workerResult.error.message}`)
    } else {
      console.log("Notification worker shut down successfully")
    }
    notificationWorker = null
  }

  // Close queue events
  if (queueEvents) {
    const eventsResult = await fromPromise(queueEvents.close())
    if (!eventsResult.success) {
      errors.push(`Queue events shutdown error: ${eventsResult.error.message}`)
    } else {
      console.log("Queue events shut down successfully")
    }
    queueEvents = null
  }

  // Close queue
  if (notificationQueue) {
    const queueResult = await fromPromise(notificationQueue.close())
    if (!queueResult.success) {
      errors.push(`Queue shutdown error: ${queueResult.error.message}`)
    } else {
      console.log("Notification queue shut down successfully")
    }
    notificationQueue = null
  }

  if (errors.length > 0) {
    return err(new Error(`Shutdown errors: ${errors.join(", ")}`))
  }

  return ok(undefined)
}

// Helper functions for common notification patterns

/**
 * Send application confirmation notification
 */
export async function queueApplicationConfirmation(
  studentEmail: string,
  studentName: string,
  delay = 0
): Promise<Result<Job<EmailJobData, NotificationJobResult>, Error>> {
  return addNotificationJob({
    type: "application_confirmation",
    to: studentEmail,
    data: { studentName },
    priority: "normal",
    delay,
  })
}

/**
 * Send match notification to student
 */
export async function queueStudentMatchNotification(
  studentEmail: string,
  studentName: string,
  matchType: "organization" | "faculty",
  matchName: string,
  nextSteps: string,
  delay = 0
): Promise<Result<Job<EmailJobData, NotificationJobResult>, Error>> {
  return addNotificationJob({
    type: "student_match",
    to: studentEmail,
    data: { studentName, matchType, matchName, nextSteps },
    priority: "high",
    delay,
  })
}

/**
 * Send support ticket notification to admin
 */
export async function queueSupportTicketNotification(
  adminEmail: string,
  adminName: string,
  ticketType: string,
  ticketDetails: string,
  studentInfo?: { name: string; email: string },
  delay = 0
): Promise<Result<Job<EmailJobData, NotificationJobResult>, Error>> {
  return addNotificationJob({
    type: "support_ticket",
    to: adminEmail,
    data: { adminName, ticketType, ticketDetails, studentInfo },
    priority: "critical",
    delay,
  })
}
