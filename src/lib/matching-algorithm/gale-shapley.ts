/**
 * Gale-Shapley Algorithm Implementation for SA1L Externship Platform
 *
 * This implements the organization-proposing variant of the Gale-Shapley algorithm
 * with support for:
 * - Capacity constraints (1-6 per org, max 5 per faculty)
 * - Co-supervision (multiple faculty per project)
 * - Application timestamp tie-breaking
 * - Two types of matching: organization-student and faculty-student
 */

import type {
  facultyProfiles,
  organizationProfiles,
  projects,
  studentAreaOfLawRankings,
  studentProfiles,
  studentResearchInterests,
  users,
} from "@/drizzle/schema"

// Type aliases for better readability
type FacultyProfile = typeof facultyProfiles.$inferSelect
type OrganizationProfile = typeof organizationProfiles.$inferSelect
type Project = typeof projects.$inferSelect
type StudentAreaOfLawRanking = typeof studentAreaOfLawRankings.$inferSelect
type StudentProfile = typeof studentProfiles.$inferSelect
type StudentResearchInterest = typeof studentResearchInterests.$inferSelect
type User = typeof users.$inferSelect

// Types for the algorithm
export interface Student {
  id: string
  profile: StudentProfile & { user: User }
  areaRankings: StudentAreaOfLawRanking[]
  researchInterests: StudentResearchInterest[]
  applicationDate: Date
}

export interface Organization {
  id: string
  profile: OrganizationProfile & { user: User }
  capacity: number
  currentMatches: string[] // Student IDs already matched
}

export interface FacultyProject {
  id: string
  project: Project
  supervisors: Array<{
    facultyId: string
    faculty: FacultyProfile & { user: User }
    isPrimary: boolean
    capacity: number
    currentMatches: string[] // Student IDs already matched
  }>
}

export interface MatchResult {
  studentId: string
  organizationId?: string
  facultyId?: string
  projectId?: string
  matchType: "organization" | "faculty"
  score: number
}

export interface ScoringWeights {
  areaPreference: number // 40%
  statementScore: number // 25%
  coreGPA: number // 25%
  lrwGPA: number // 10%
}

const DEFAULT_WEIGHTS: ScoringWeights = {
  areaPreference: 0.4,
  statementScore: 0.25,
  coreGPA: 0.25,
  lrwGPA: 0.1,
}

/**
 * Calculate the preference score between a student and organization
 */
export function calculateOrganizationScore(
  student: Student,
  organization: Organization,
  weights: ScoringWeights = DEFAULT_WEIGHTS
): number {
  let score = 0

  // Area preference match (40%)
  if (organization.profile.areaOfLawId && student.areaRankings.length > 0) {
    const ranking = student.areaRankings.find(
      (r) => r.areaOfLawId === organization.profile.areaOfLawId
    )
    if (ranking) {
      // Convert ranking (1-5) to score: 1st choice = 100%, 5th choice = 20%
      const rankScore = 100 - (ranking.ranking - 1) * 20
      score += rankScore * weights.areaPreference
    }
  }

  // Statement score (25%)
  if (student.profile.statementScore) {
    score +=
      (student.profile.statementScore / 25) * 100 * weights.statementScore
  }

  // Core GPA (25%)
  if (student.profile.coreGPA) {
    // Grade scale: A+ = 5.0 on 40-point scale (-0.25 per step)
    const gpaScore = (Number(student.profile.coreGPA) / 5) * 100
    score += gpaScore * weights.coreGPA
  }

  // LRW GPA (10%)
  if (student.profile.lrwGPA) {
    const lrwScore = (Number(student.profile.lrwGPA) / 5) * 100
    score += lrwScore * weights.lrwGPA
  }

  return Math.round(score)
}

/**
 * Calculate the preference score between a student and faculty project
 */
export function calculateFacultyScore(
  student: Student,
  project: FacultyProject,
  weights: ScoringWeights = DEFAULT_WEIGHTS
): number {
  let score = 0

  // Check if student expressed interest in any supervisor's research
  const hasInterest = student.researchInterests.some((interest) =>
    project.supervisors.some((s) => s.facultyId === interest.facultyId)
  )

  if (hasInterest) {
    // Student explicitly interested in this faculty's research
    score += 50 // Base interest score
  }

  // Academic performance scores (same weights as org matching for consistency)
  if (student.profile.statementScore) {
    score +=
      (student.profile.statementScore / 25) * 100 * weights.statementScore
  }

  if (student.profile.coreGPA) {
    const gpaScore = (Number(student.profile.coreGPA) / 5) * 100
    score += gpaScore * weights.coreGPA
  }

  if (student.profile.lrwGPA) {
    const lrwScore = (Number(student.profile.lrwGPA) / 5) * 100
    score += lrwScore * weights.lrwGPA
  }

  return Math.round(score)
}

/**
 * Create preference lists for students and organizations/faculty
 * Students rank based on their preferences
 * Organizations/Faculty rank based on calculated scores
 */
function createPreferenceLists(
  students: Student[],
  organizations: Organization[],
  weights: ScoringWeights
): {
  studentPreferences: Map<string, string[]> // Student ID -> ordered list of org IDs
  organizationPreferences: Map<string, string[]> // Org ID -> ordered list of student IDs
} {
  const studentPreferences = new Map<string, string[]>()
  const organizationPreferences = new Map<string, string[]>()

  // Create student preference lists based on their area rankings
  for (const student of students) {
    const preferences: Array<{ orgId: string; score: number }> = []

    for (const org of organizations) {
      // Check if geographic restrictions apply based on work arrangements
      const studentWantsRemote =
        student.profile.workArrangementPreference &&
        Array.isArray(student.profile.workArrangementPreference) &&
        student.profile.workArrangementPreference.includes("remote")

      const orgOffersRemote =
        org.profile.remoteAvailable ||
        (org.profile.workArrangements &&
          Array.isArray(org.profile.workArrangements) &&
          org.profile.workArrangements.includes("remote"))

      // Apply geographic restrictions only for non-remote positions
      if (
        student.profile.geographicPreferences &&
        Array.isArray(student.profile.geographicPreferences) &&
        student.profile.geographicPreferences.length > 0
      ) {
        // If org offers remote and student is open to remote, geography doesn't matter
        if (!(studentWantsRemote && orgOffersRemote)) {
          // Otherwise, org must be in one of student's preferred locations
          if (
            !org.profile.locationId ||
            !student.profile.geographicPreferences.includes(
              org.profile.locationId
            )
          ) {
            continue // Skip this org - not in student's preferred locations
          }
        }
      }

      const score = calculateOrganizationScore(student, org, weights)
      if (score > 0) {
        // Only include organizations with positive scores
        preferences.push({ orgId: org.id, score })
      }
    }

    // Sort by score descending, then by org name for stability
    preferences.sort((a, b) => {
      if (b.score !== a.score) return b.score - a.score
      return a.orgId.localeCompare(b.orgId)
    })

    studentPreferences.set(
      student.id,
      preferences.map((p) => p.orgId)
    )
  }

  // Create organization preference lists based on calculated scores
  for (const org of organizations) {
    const preferences: Array<{
      studentId: string
      score: number
      timestamp: Date
    }> = []

    for (const student of students) {
      // Check if geographic restrictions apply based on work arrangements
      const studentWantsRemote =
        student.profile.workArrangementPreference &&
        Array.isArray(student.profile.workArrangementPreference) &&
        student.profile.workArrangementPreference.includes("remote")

      const orgOffersRemote =
        org.profile.remoteAvailable ||
        (org.profile.workArrangements &&
          Array.isArray(org.profile.workArrangements) &&
          org.profile.workArrangements.includes("remote"))

      // Apply geographic restrictions only for non-remote positions
      if (
        student.profile.geographicPreferences &&
        Array.isArray(student.profile.geographicPreferences) &&
        student.profile.geographicPreferences.length > 0
      ) {
        // If org offers remote and student is open to remote, geography doesn't matter
        if (!(studentWantsRemote && orgOffersRemote)) {
          // Otherwise, student must have selected this org's location
          if (
            !org.profile.locationId ||
            !student.profile.geographicPreferences.includes(
              org.profile.locationId
            )
          ) {
            continue // Skip this student - org not in their preferred locations
          }
        }
      }

      const score = calculateOrganizationScore(student, org, weights)
      if (score >= 50) {
        // Minimum threshold of 50%
        preferences.push({
          studentId: student.id,
          score,
          timestamp: student.applicationDate,
        })
      }
    }

    // Sort by score descending, then by application timestamp (earlier is better)
    preferences.sort((a, b) => {
      if (b.score !== a.score) return b.score - a.score
      return a.timestamp.getTime() - b.timestamp.getTime()
    })

    organizationPreferences.set(
      org.id,
      preferences.map((p) => p.studentId)
    )
  }

  return { studentPreferences, organizationPreferences }
}

/**
 * Gale-Shapley Algorithm for Organization Matching
 * Organizations propose to students
 */
export function runOrganizationMatching(
  students: Student[],
  organizations: Organization[],
  weights: ScoringWeights = DEFAULT_WEIGHTS
): MatchResult[] {
  const { studentPreferences, organizationPreferences } = createPreferenceLists(
    students,
    organizations,
    weights
  )

  // Track current matches and proposals
  const studentMatches = new Map<string, string>() // Student ID -> Org ID
  const organizationProposals = new Map<string, number>() // Org ID -> next proposal index

  // Initialize proposal indices
  for (const org of organizations) {
    organizationProposals.set(org.id, 0)
  }

  // Main algorithm loop
  let hasUnmatchedOrgs = true
  while (hasUnmatchedOrgs) {
    hasUnmatchedOrgs = false

    for (const org of organizations) {
      // Skip if org is at capacity
      if (org.currentMatches.length >= org.capacity) continue

      const proposalIndex = organizationProposals.get(org.id) || 0
      const preferences = organizationPreferences.get(org.id) || []

      // Skip if org has exhausted all preferences
      if (proposalIndex >= preferences.length) continue

      hasUnmatchedOrgs = true
      const studentId = preferences[proposalIndex]

      // Move to next preference for next iteration
      organizationProposals.set(org.id, proposalIndex + 1)

      // Skip if student is already matched to this org
      if (org.currentMatches.includes(studentId)) continue

      const currentMatch = studentMatches.get(studentId)

      if (!currentMatch) {
        // Student is unmatched, accept proposal
        studentMatches.set(studentId, org.id)
        org.currentMatches.push(studentId)
      } else {
        // Student is matched, check if they prefer this org
        const studentPrefs = studentPreferences.get(studentId) || []
        const currentIndex = studentPrefs.indexOf(currentMatch)
        const newIndex = studentPrefs.indexOf(org.id)

        if (
          newIndex !== -1 &&
          (currentIndex === -1 || newIndex < currentIndex)
        ) {
          // Student prefers new org
          studentMatches.set(studentId, org.id)

          // Remove from current org
          const currentOrg = organizations.find((o) => o.id === currentMatch)
          if (currentOrg) {
            currentOrg.currentMatches = currentOrg.currentMatches.filter(
              (id) => id !== studentId
            )
          }

          // Add to new org
          org.currentMatches.push(studentId)
        }
      }
    }
  }

  // Convert matches to results
  const results: MatchResult[] = []
  for (const [studentId, orgId] of studentMatches.entries()) {
    const student = students.find((s) => s.id === studentId)!
    const org = organizations.find((o) => o.id === orgId)!
    const score = calculateOrganizationScore(student, org, weights)

    results.push({
      studentId,
      organizationId: orgId,
      matchType: "organization",
      score,
    })
  }

  return results
}

/**
 * Gale-Shapley Algorithm for Faculty Matching
 * Projects (faculty groups) propose to students
 */
export function runFacultyMatching(
  students: Student[],
  projects: FacultyProject[],
  weights: ScoringWeights = DEFAULT_WEIGHTS
): MatchResult[] {
  const projectPreferences = new Map<string, string[]>() // Project ID -> ordered list of student IDs
  const studentPreferences = new Map<string, string[]>() // Student ID -> ordered list of project IDs

  // Create preference lists for projects
  for (const project of projects) {
    const preferences: Array<{
      studentId: string
      score: number
      timestamp: Date
    }> = []

    for (const student of students) {
      const score = calculateFacultyScore(student, project, weights)
      if (score >= 40) {
        // Lower threshold for faculty matching
        preferences.push({
          studentId: student.id,
          score,
          timestamp: student.applicationDate,
        })
      }
    }

    // Sort by score descending, then by application timestamp
    preferences.sort((a, b) => {
      if (b.score !== a.score) return b.score - a.score
      return a.timestamp.getTime() - b.timestamp.getTime()
    })

    projectPreferences.set(
      project.id,
      preferences.map((p) => p.studentId)
    )
  }

  // Create student preference lists based on research interests
  for (const student of students) {
    const preferences: Array<{ projectId: string; score: number }> = []

    for (const project of projects) {
      const score = calculateFacultyScore(student, project, weights)
      if (score > 0) {
        preferences.push({ projectId: project.id, score })
      }
    }

    // Sort by score descending
    preferences.sort((a, b) => b.score - a.score)
    studentPreferences.set(
      student.id,
      preferences.map((p) => p.projectId)
    )
  }

  // Track matches and proposals
  const studentMatches = new Map<
    string,
    { projectId: string; facultyId: string }
  >()
  const projectProposals = new Map<string, number>()

  // Initialize proposal indices
  for (const project of projects) {
    projectProposals.set(project.id, 0)
  }

  // Main algorithm loop
  let hasUnmatchedProjects = true
  while (hasUnmatchedProjects) {
    hasUnmatchedProjects = false

    for (const project of projects) {
      // Check if ALL supervisors are at capacity
      const allAtCapacity = project.supervisors.every(
        (s) => s.currentMatches.length >= s.capacity
      )
      if (allAtCapacity) continue

      const proposalIndex = projectProposals.get(project.id) || 0
      const preferences = projectPreferences.get(project.id) || []

      if (proposalIndex >= preferences.length) continue

      hasUnmatchedProjects = true
      const studentId = preferences[proposalIndex]

      projectProposals.set(project.id, proposalIndex + 1)

      // Skip if student is already matched to any supervisor in this project
      const alreadyMatchedToProject = project.supervisors.some((s) =>
        s.currentMatches.includes(studentId)
      )
      if (alreadyMatchedToProject) continue

      const currentMatch = studentMatches.get(studentId)

      if (!currentMatch) {
        // Student is unmatched, accept proposal
        // Assign to primary supervisor by default
        const primarySupervisor = project.supervisors.find((s) => s.isPrimary)!
        studentMatches.set(studentId, {
          projectId: project.id,
          facultyId: primarySupervisor.facultyId,
        })
        primarySupervisor.currentMatches.push(studentId)
      } else {
        // Student is matched, check if they prefer this project
        const studentPrefs = studentPreferences.get(studentId) || []
        const currentIndex = studentPrefs.indexOf(currentMatch.projectId)
        const newIndex = studentPrefs.indexOf(project.id)

        if (
          newIndex !== -1 &&
          (currentIndex === -1 || newIndex < currentIndex)
        ) {
          // Student prefers new project
          const primarySupervisor = project.supervisors.find(
            (s) => s.isPrimary
          )!
          studentMatches.set(studentId, {
            projectId: project.id,
            facultyId: primarySupervisor.facultyId,
          })

          // Remove from current project
          const currentProject = projects.find(
            (p) => p.id === currentMatch.projectId
          )
          if (currentProject) {
            for (const supervisor of currentProject.supervisors) {
              supervisor.currentMatches = supervisor.currentMatches.filter(
                (id) => id !== studentId
              )
            }
          }

          // Add to new project
          primarySupervisor.currentMatches.push(studentId)
        }
      }
    }
  }

  // Convert matches to results
  const results: MatchResult[] = []
  for (const [studentId, match] of studentMatches.entries()) {
    const student = students.find((s) => s.id === studentId)!
    const project = projects.find((p) => p.id === match.projectId)!
    const score = calculateFacultyScore(student, project, weights)

    results.push({
      studentId,
      facultyId: match.facultyId,
      projectId: match.projectId,
      matchType: "faculty",
      score,
    })
  }

  return results
}
