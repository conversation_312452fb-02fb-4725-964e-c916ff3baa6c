/**
 * Tests for Gale-Shapley algorithm with geographic restrictions
 */

import { describe, expect, it } from "vitest"

import type { Organization, Student } from "./gale-shapley"

import { calculateOrganizationScore, runOrganizationMatching } from "./gale-shapley"

describe("Geographic Restrictions in Gale-Shapley Algorithm", () => {
  // Mock location IDs
  const LOCATION_TORONTO = "loc-1"
  const LOCATION_VANCOUVER = "loc-2"
  const LOCATION_MONTREAL = "loc-3"

  // Helper to create mock student
  const createMockStudent = (
    id: string,
    geographicPreferences: string[],
    coreGPA: string = "4.0",
    workArrangementPreference: string[] = ["in_person"]
  ): Student => ({
    id,
    profile: {
      id,
      userId: id,
      coreGPA,
      lrwGPA: "4.0",
      statementScore: 20,
      geographicPreferences,
      workArrangementPreference,
      submittedAt: new Date(),
      user: { id, email: `student${id}@test.com`, name: `Student ${id}` },
    } as any,
    areaRankings: [
      {
        id: "1",
        studentProfileId: id,
        areaOfLawId: "area-1",
        ranking: 1,
      },
    ],
    researchInterests: [],
    applicationDate: new Date(),
  })

  // Helper to create mock organization
  const createMockOrg = (
    id: string,
    locationId: string,
    capacity: number = 2,
    remoteAvailable: boolean = false,
    workArrangements: string[] = ["in_person"]
  ): Organization => ({
    id,
    profile: {
      id,
      userId: id,
      locationId,
      areaOfLawId: "area-1",
      remoteAvailable,
      workArrangements,
      user: { id, email: `org${id}@test.com`, name: `Org ${id}` },
    } as any,
    capacity,
    currentMatches: [],
  })

  it("should only match students to organizations in their preferred locations", () => {
    const students: Student[] = [
      createMockStudent("s1", [LOCATION_TORONTO]), // Only wants Toronto
      createMockStudent("s2", [LOCATION_VANCOUVER]), // Only wants Vancouver
      createMockStudent("s3", [LOCATION_TORONTO, LOCATION_VANCOUVER]), // Wants either
    ]

    const organizations: Organization[] = [
      createMockOrg("o1", LOCATION_TORONTO), // Toronto org
      createMockOrg("o2", LOCATION_VANCOUVER), // Vancouver org
      createMockOrg("o3", LOCATION_MONTREAL), // Montreal org
    ]

    const results = runOrganizationMatching(students, organizations)

    // s1 should only match with Toronto org
    const s1Match = results.find((r: any) => r.studentId === "s1")
    expect(s1Match?.organizationId).toBe("o1")

    // s2 should only match with Vancouver org
    const s2Match = results.find((r: any) => r.studentId === "s2")
    expect(s2Match?.organizationId).toBe("o2")

    // s3 could match with either Toronto or Vancouver (but not Montreal)
    const s3Match = results.find((r: any) => r.studentId === "s3")
    expect(["o1", "o2"]).toContain(s3Match?.organizationId)

    // No one should match with Montreal org since no students selected it
    const montrealMatches = results.filter((r: any) => r.organizationId === "o3")
    expect(montrealMatches).toHaveLength(0)
  })

  it("should allow students without geographic preferences to match anywhere", () => {
    const students: Student[] = [
      createMockStudent("s1", []), // No location preferences - flexible
    ]

    const organizations: Organization[] = [
      createMockOrg("o1", LOCATION_TORONTO),
      createMockOrg("o2", LOCATION_VANCOUVER),
    ]

    const results = runOrganizationMatching(students, organizations)

    // Student with no preferences can match with any org (flexible location)
    expect(results).toHaveLength(1)
    expect(["o1", "o2"]).toContain(results[0].organizationId)
  })

  it("should handle capacity constraints with geographic restrictions and weighted scoring", () => {
    // Create students with varied profiles to show weighted scoring prevents GPA dominance
    const students: Student[] = [
      {
        ...createMockStudent("s1", [LOCATION_TORONTO], "5.0"), // Perfect GPA but poor statement
        profile: {
          ...createMockStudent("s1", [LOCATION_TORONTO], "5.0").profile,
          statementScore: 10, // Low statement score
          lrwGPA: "3.0", // Lower LRW
        },
      },
      {
        ...createMockStudent("s2", [LOCATION_TORONTO], "3.5"), // Lower GPA but excellent overall
        profile: {
          ...createMockStudent("s2", [LOCATION_TORONTO], "3.5").profile,
          statementScore: 25, // Perfect statement score
          lrwGPA: "4.5", // Strong LRW
        },
      },
      {
        ...createMockStudent("s3", [LOCATION_TORONTO], "4.0"), // Balanced student
        profile: {
          ...createMockStudent("s3", [LOCATION_TORONTO], "4.0").profile,
          statementScore: 20, // Good statement
          lrwGPA: "4.0", // Good LRW
        },
      },
      createMockStudent("s4", [LOCATION_VANCOUVER], "5.0"), // Different location
    ]

    const organizations: Organization[] = [
      createMockOrg("o1", LOCATION_TORONTO, 2), // Toronto org with capacity 2
      createMockOrg("o2", LOCATION_VANCOUVER, 2), // Vancouver org
    ]

    const results = runOrganizationMatching(students, organizations)

    // Toronto org should match based on weighted scores, not just GPA
    const torontoMatches = results.filter((r: any) => r.organizationId === "o1")
    expect(torontoMatches).toHaveLength(2)

    // s2 should be matched despite lower GPA due to excellent statement and good LRW
    const s2Match = results.find((r: any) => r.studentId === "s2")
    expect(s2Match).toBeDefined()
    expect(s2Match?.organizationId).toBe("o1")

    // Vancouver org should only match s4
    const vancouverMatches = results.filter((r: any) => r.organizationId === "o2")
    expect(vancouverMatches).toHaveLength(1)
    expect(vancouverMatches[0].studentId).toBe("s4")
  })

  it("should calculate score as 0 for organizations not in student preferences", () => {
    const student = createMockStudent("s1", [LOCATION_TORONTO])
    const torontoOrg = createMockOrg("o1", LOCATION_TORONTO)
    const vancouverOrg = createMockOrg("o2", LOCATION_VANCOUVER)

    // This tests the internal scoring, but in the actual algorithm,
    // the geographic filtering happens before score calculation
    const torontoScore = calculateOrganizationScore(student, torontoOrg)
    const vancouverScore = calculateOrganizationScore(student, vancouverOrg)

    expect(torontoScore).toBeGreaterThan(0)
    // Note: calculateOrganizationScore doesn't check geographic preferences
    // The filtering happens in createPreferenceLists
  })

  it("should demonstrate weighted scoring prevents GPA-only dominance", () => {
    // Test that a student with perfect GPA but poor other metrics scores lower
    // than a balanced student, showing the 40/25/25/10 weighting works
    const perfectGPAStudent = createMockStudent("s1", [LOCATION_TORONTO])
    perfectGPAStudent.profile.coreGPA = "5.0" // 25% weight
    perfectGPAStudent.profile.lrwGPA = "5.0" // 10% weight
    perfectGPAStudent.profile.statementScore = 0 // 25% weight
    perfectGPAStudent.areaRankings = [] // 40% weight - no area match

    const balancedStudent = createMockStudent("s2", [LOCATION_TORONTO])
    balancedStudent.profile.coreGPA = "3.5" // 25% weight
    balancedStudent.profile.lrwGPA = "3.5" // 10% weight
    balancedStudent.profile.statementScore = 20 // 25% weight
    balancedStudent.areaRankings = [
      {
        id: "1",
        studentProfileId: "s2",
        areaOfLawId: "area-1",
        ranking: 1, // First choice - 40% weight
      },
    ]

    const org = createMockOrg("o1", LOCATION_TORONTO)

    const perfectGPAScore = calculateOrganizationScore(perfectGPAStudent, org)
    const balancedScore = calculateOrganizationScore(balancedStudent, org)

    // Perfect GPA student: (5/5)*100*0.25 + (5/5)*100*0.10 = 25 + 10 = 35
    // Balanced student: (3.5/5)*100*0.25 + (3.5/5)*100*0.10 + (20/25)*100*0.25 + 100*0.40 = 17.5 + 7 + 20 + 40 = 84.5

    expect(balancedScore).toBeGreaterThan(perfectGPAScore)
    expect(perfectGPAScore).toBe(35) // Only gets GPA points
    expect(balancedScore).toBe(85) // Gets points from all categories (rounded)
  })

  it("should allow remote-preferring students to match with remote orgs regardless of location", () => {
    const students: Student[] = [
      // Student wants remote work, only selected Toronto for in-person
      createMockStudent("s1", [LOCATION_TORONTO], "4.0", ["remote", "hybrid"]),
      // Student only wants in-person in Toronto
      createMockStudent("s2", [LOCATION_TORONTO], "4.0", ["in_person"]),
      // Student flexible on both location and work arrangement
      createMockStudent("s3", [], "4.0", ["remote", "in_person", "hybrid"]),
    ]

    const organizations: Organization[] = [
      // Remote org in Vancouver
      createMockOrg("o1", LOCATION_VANCOUVER, 2, true, ["remote"]),
      // In-person only org in Toronto
      createMockOrg("o2", LOCATION_TORONTO, 1, false, ["in_person"]),
      // Hybrid org in Montreal
      createMockOrg("o3", LOCATION_MONTREAL, 1, false, ["hybrid", "remote"]),
    ]

    const results = runOrganizationMatching(students, organizations)

    // s1 should match with Vancouver remote org despite not selecting Vancouver
    const s1Match = results.find((r: any) => r.studentId === "s1")
    expect(s1Match?.organizationId).toBe("o1")

    // s2 should match with Toronto in-person org
    const s2Match = results.find((r: any) => r.studentId === "s2")
    expect(s2Match?.organizationId).toBe("o2")

    // s3 can match with any remaining org (likely Montreal hybrid)
    const s3Match = results.find((r: any) => r.studentId === "s3")
    expect(s3Match).toBeDefined()
  })

  it("should not match in-person-only students with remote orgs outside their locations", () => {
    const students: Student[] = [
      // Student only wants in-person in Toronto
      createMockStudent("s1", [LOCATION_TORONTO], "4.0", ["in_person"]),
    ]

    const organizations: Organization[] = [
      // Remote org in Vancouver
      createMockOrg("o1", LOCATION_VANCOUVER, 1, true, ["remote"]),
    ]

    const results = runOrganizationMatching(students, organizations)

    // s1 should not match because they want in-person only and Vancouver is not in their locations
    expect(results).toHaveLength(0)
  })

  it("should respect student preference order when multiple orgs in same location", () => {
    const students: Student[] = [
      {
        ...createMockStudent("s1", [LOCATION_TORONTO]),
        areaRankings: [
          {
            id: "1",
            studentProfileId: "s1",
            areaOfLawId: "area-1",
            ranking: 1,
          },
          {
            id: "2",
            studentProfileId: "s1",
            areaOfLawId: "area-2",
            ranking: 2,
          },
        ],
      },
    ]

    const organizations: Organization[] = [
      {
        ...createMockOrg("o1", LOCATION_TORONTO),
        profile: {
          ...createMockOrg("o1", LOCATION_TORONTO).profile,
          areaOfLawId: "area-2",
        },
      },
      {
        ...createMockOrg("o2", LOCATION_TORONTO),
        profile: {
          ...createMockOrg("o2", LOCATION_TORONTO).profile,
          areaOfLawId: "area-1",
        },
      },
    ]

    const results = runOrganizationMatching(students, organizations)

    // Student should match with o2 (area-1) since it's their first choice
    const match = results.find((r: any) => r.studentId === "s1")
    expect(match?.organizationId).toBe("o2")
  })
})
